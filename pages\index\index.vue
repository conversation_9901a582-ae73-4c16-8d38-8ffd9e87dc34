<template>
	<view class="container">
		<!-- 顶部装饰 -->
		<view class="header-decoration">
			<view class="floating-heart heart1">💕</view>
			<view class="floating-heart heart2">💖</view>
			<view class="floating-heart heart3">💝</view>
		</view>
		
		<!-- 主标题区域 -->
		<view class="title-section">
			<text class="main-title">甜蜜时光</text>
			<text class="sub-title">记录我们的每一个美好瞬间</text>
		</view>
		
		<!-- 功能卡片区域 -->
		<view class="feature-cards">
			<view class="feature-card" @tap="navigateTo('/pages/login/login')">
				<view class="card-icon">💑</view>
				<text class="card-title">开始恋爱</text>
				<text class="card-desc">记录甜蜜时光</text>
			</view>
			
			<view class="feature-card" @tap="navigateTo('/pages/user/user')">
				<view class="card-icon">👫</view>
				<text class="card-title">我们的空间</text>
				<text class="card-desc">查看情侣档案</text>
			</view>
			
			<view class="feature-card">
				<view class="card-icon">📷</view>
				<text class="card-title">甜蜜相册</text>
				<text class="card-desc">珍藏美好回忆</text>
			</view>
			
			<view class="feature-card">
				<view class="card-icon">💌</view>
				<text class="card-title">情话绵绵</text>
				<text class="card-desc">写给彼此的话</text>
			</view>
			
			<view class="feature-card">
				<view class="card-icon">🎂</view>
				<text class="card-title">纪念日</text>
				<text class="card-desc">重要日子提醒</text>
			</view>
			
			<view class="feature-card">
				<view class="card-icon">🎁</view>
				<text class="card-title">愿望清单</text>
				<text class="card-desc">想要的小礼物</text>
			</view>
		</view>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			}
		},
		methods: {
			navigateTo(url) {
				uni.navigateTo({
					url: url
				});
			}
		}
	}
</script>

<style scoped>
	.container {
		min-height: 100vh;
		background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
		padding: 40rpx 30rpx;
		position: relative;
	}
	
	/* 顶部装饰 */
	.header-decoration {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 200rpx;
		pointer-events: none;
	}
	
	.floating-heart {
		position: absolute;
		font-size: 40rpx;
		opacity: 0.6;
		animation: float 4s ease-in-out infinite;
	}
	
	.heart1 {
		top: 60rpx;
		left: 10%;
		animation-delay: 0s;
	}
	
	.heart2 {
		top: 100rpx;
		right: 15%;
		animation-delay: 1.5s;
	}
	
	.heart3 {
		top: 140rpx;
		left: 50%;
		animation-delay: 3s;
	}
	
	/* 主标题 */
	.title-section {
		text-align: center;
		margin: 120rpx 0 80rpx;
	}
	
	.main-title {
		font-size: 56rpx;
		font-weight: 700;
		color: #fff;
		text-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
		display: block;
		margin-bottom: 20rpx;
	}
	
	.sub-title {
		font-size: 28rpx;
		color: rgba(255,255,255,0.9);
		font-weight: 400;
	}
	
	/* 功能卡片 */
	.feature-cards {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 30rpx;
		margin-bottom: 80rpx;
	}
	
	.feature-card {
		background: rgba(255,255,255,0.95);
		border-radius: 24rpx;
		padding: 40rpx 30rpx;
		text-align: center;
		box-shadow: 0 8rpx 24rpx rgba(255,154,158,0.2);
		transition: all 0.3s ease;
		backdrop-filter: blur(10rpx);
	}
	
	.feature-card:active {
		transform: scale(0.95);
		box-shadow: 0 4rpx 12rpx rgba(255,154,158,0.3);
	}
	
	.card-icon {
		font-size: 48rpx;
		margin-bottom: 20rpx;
		display: block;
	}
	
	.card-title {
		font-size: 30rpx;
		font-weight: 600;
		color: #333;
		display: block;
		margin-bottom: 10rpx;
	}
	
	.card-desc {
		font-size: 24rpx;
		color: #999;
		line-height: 1.4;
	}
	
	/* 登录提示 */
	.login-prompt {
		text-align: center;
		background: rgba(255,255,255,0.9);
		border-radius: 24rpx;
		padding: 40rpx;
		box-shadow: 0 8rpx 24rpx rgba(255,154,158,0.2);
	}
	
	.prompt-text {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 30rpx;
		display: block;
	}
	
	.login-btn {
		background: linear-gradient(135deg, #ff6b9d, #c44569);
		color: #fff;
		border: none;
		border-radius: 50rpx;
		padding: 24rpx 60rpx;
		font-size: 30rpx;
		font-weight: 600;
		box-shadow: 0 6rpx 20rpx rgba(196,69,105,0.3);
		transition: all 0.3s ease;
	}
	
	.login-btn:active {
		transform: translateY(2rpx);
		box-shadow: 0 4rpx 16rpx rgba(196,69,105,0.4);
	}
	
	/* 动画 */
	@keyframes float {
		0%, 100% {
			transform: translateY(0px);
		}
		50% {
			transform: translateY(-20rpx);
		}
	}
</style>