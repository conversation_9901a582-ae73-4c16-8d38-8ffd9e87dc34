<template>
	<view class="login-container">
		<!-- 顶部装饰 -->
		<view class="top-decoration">
			<view class="love-icon">💕</view>
		</view>
		
		<!-- 登录卡片 -->
		<view class="login-card">
			<view class="logo-section">
				<view class="app-logo">💑</view>
				<text class="app-name">甜蜜时光</text>
				<text class="app-slogan">记录我们的爱情故事</text>
			</view>
			
			<!-- 微信登录按钮 -->
			<button class="wechat-login-btn" @click="handleWechatLogin">
				<view class="btn-content">
					<text class="wechat-icon">💚</text>
					<text class="btn-text">微信一键登录</text>
				</view>
			</button>
			
		<!-- 用户协议 -->
		<view class="agreement-section">
			<view class="agreement-check">
				<checkbox-group @change="onAgreementChange">
					<checkbox value="agree" :checked="isAgreed" color="#ff6b9d"/>
				</checkbox-group>
				<text class="agreement-text">
					我已阅读并同意
					<text class="link-text">《用户协议》</text>
					和
					<text class="link-text">《隐私政策》</text>
				</text>
			</view>
		</view>
		
		<!-- 底部装饰 -->
		<view class="bottom-decoration">
			<view class="heart-animation">
				<text class="heart">💖</text>
				<text class="heart">💕</text>
				<text class="heart">💝</text>
			</view>
		</view>
	</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				verifyCode: '',
				isAgreed: false,
				countdown: 0,
				userInfo: null,
				isLoading: false
			}
		},
		methods: {
			// 微信登录
		async	handleWechatLogin() {
			//判断勾选协议
			console.log("登录点击拉")
				if (!this.isAgreed) {
					uni.showToast({
						title: '请先同意用户协议',
						icon: 'none'
					});
					return;
				}
				if(!uni.getStorageInfoSync("userInfo")){
					//用户登录功能
					try{
						 uni.showLoading({title: '登录中...'});
						const userProfileRes=await new Promise((resolve,reject)=>{
							uni.getUserProfile({
								desc:"登录后可以同步数据",
								success:resolve,
								fail:reject
							})
						},1000)
						const userInfo=userProfileRes.userInfo
						this.userInfo=userInfo
						//获取临时code‘
						const loginRes=await new Promise((resolve,reject)=>{
							uni.login({
								provider:'weixin',
								success:resolve,
								fail:reject
							})
						},1000)
						const code =loginRes.code;
						if(!code){
							uni.showToast({
								title:"获取code失败",
								icon:"none"
							})
							return
						}
						const saveRes=await uniCloud.callFunction({
							name:"save-user-infor",
							data:{
								'nickname':userInfo.nickname,
								'avatarUrl':userInfo.avatarUrl,
								'gender':userInfo.gender,
								'code':code,
								}
						},1000)
						uni.setStorageSync('userInfo',this.userInfo)
						uni.showToast({
							title:'登录成功',
							icon:"success"
						})
						uni.switchTab({
							url:"/pages/index/index"
						})
						console.log(userInfo)
					}catch(err){
						uni.showToast({
							title:"登录失败",
							icon:"none"
						})
					}
				}else{
					uni.showToast({
						title:'登录成功',
						icon:"success"
					})
					uni.switchTab({
						url:"/pages/index/index"
					})
				}

},
//勾选用户协议
onAgreementChange(){
	this.isAgreed=true
}
}}
</script>

<style scoped>
	.login-container {
		min-height: 100vh;
		background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
		padding: 40rpx;
		position: relative;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}
	
	/* 顶部装饰 */
	.top-decoration {
		text-align: center;
		margin-bottom: 60rpx;
	}
	
	.love-icon {
		font-size: 80rpx;
		animation: pulse 2s infinite;
	}
	
	/* 登录卡片 */
	.login-card {
		background: rgba(255,255,255,0.95);
		border-radius: 32rpx;
		padding: 60rpx 40rpx;
		box-shadow: 0 12rpx 40rpx rgba(255,154,158,0.25);
		backdrop-filter: blur(10rpx);
	}
	
	/* Logo区域 */
	.logo-section {
		text-align: center;
		margin-bottom: 60rpx;
	}
	
	.app-logo {
		font-size: 100rpx;
		margin-bottom: 20rpx;
		display: block;
	}
	
	.app-name {
		font-size: 48rpx;
		font-weight: 700;
		color: #333;
		display: block;
		margin-bottom: 10rpx;
	}
	
	.app-slogan {
		font-size: 28rpx;
		color: #999;
	}
	
	/* 微信登录按钮 */
	.wechat-login-btn {
		width: 100%;
		background: #07c160;
		border: none;
		border-radius: 50rpx;
		padding: 32rpx;
		margin-bottom: 40rpx;
		box-shadow: 0 8rpx 24rpx rgba(7,193,96,0.3);
		transition: all 0.3s ease;
	}
	
	.wechat-login-btn:active {
		transform: translateY(2rpx);
		box-shadow: 0 6rpx 20rpx rgba(7,193,96,0.4);
	}
	
	.btn-content {
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.wechat-icon {
		font-size: 36rpx;
		margin-right: 16rpx;
	}
	
	.btn-text {
		font-size: 32rpx;
		color: #fff;
		font-weight: 600;
	}
	
	/* 手机号登录 */
	.phone-login-section {
		border-top: 1rpx solid #f0f0f0;
		padding-top: 40rpx;
	}
	
	.section-title {
		text-align: center;
		font-size: 28rpx;
		color: #999;
		margin-bottom: 40rpx;
		display: block;
	}
	
	.input-group {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
		background: #f8f8f8;
		border-radius: 50rpx;
		padding: 0 30rpx;
		height: 100rpx;
	}
	
	.phone-input, .code-input {
		flex: 1;
		font-size: 30rpx;
		color: #333;
		background: transparent;
		border: none;
	}
	
	.get-code-btn {
		background: linear-gradient(135deg, #ff6b9d, #c44569);
		color: #fff;
		border: none;
		border-radius: 40rpx;
		padding: 16rpx 32rpx;
		font-size: 26rpx;
		font-weight: 500;
	}
	
	.phone-login-btn {
		width: 100%;
		background: linear-gradient(135deg, #ff6b9d, #c44569);
		color: #fff;
		border: none;
		border-radius: 50rpx;
		padding: 32rpx;
		font-size: 32rpx;
		font-weight: 600;
		box-shadow: 0 8rpx 24rpx rgba(196,69,105,0.3);
		transition: all 0.3s ease;
	}
	
	.phone-login-btn:active {
		transform: translateY(2rpx);
		box-shadow: 0 6rpx 20rpx rgba(196,69,105,0.4);
	}
	
	/* 用户协议 */
	.agreement-section {
		margin-top: 40rpx;
		text-align: center;
	}
	
	.agreement-check {
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.agreement-text {
		font-size: 24rpx;
		color: #999;
		margin-left: 16rpx;
	}
	
	.link-text {
		color: #ff6b9d;
	}
	
	/* 底部装饰 */
	.bottom-decoration {
		position: fixed;
		bottom: 60rpx;
		left: 50%;
		transform: translateX(-50%);
	}
	
	.heart-animation {
		display: flex;
		gap: 20rpx;
	}
	
	.heart {
		font-size: 32rpx;
		opacity: 0.6;
		animation: bounce 2s infinite;
	}
	
	.heart:nth-child(2) {
		animation-delay: 0.5s;
	}
	
	.heart:nth-child(3) {
		animation-delay: 1s;
	}
	
	/* 动画 */
	@keyframes pulse {
		0%, 100% {
			transform: scale(1);
		}
		50% {
			transform: scale(1.1);
		}
	}
	
	@keyframes bounce {
		0%, 100% {
			transform: translateY(0);
		}
		50% {
			transform: translateY(-10rpx);
		}
	}
</style>