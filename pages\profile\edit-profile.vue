<template>
	<view class="container">
		<!-- 背景装饰 -->
		<view class="bg-decoration">
			<view class="decoration-circle circle-1"></view>
			<view class="decoration-circle circle-2"></view>
			<view class="decoration-circle circle-3"></view>
		</view>

		<!-- 头像编辑区域 -->
		<view class="avatar-section">
			<view class="avatar-card">
				<view class="avatar-container" @tap="changeAvatar">
					<image :src="userProfile.avatarUrl || defaultAvatar" class="profile-avatar" />
					<view class="avatar-border"></view>
					<view class="camera-icon">📷</view>
				</view>
				<text class="avatar-tip">点击更换头像</text>
			</view>
		</view>

		<!-- 基本信息编辑 -->
		<view class="form-section">
			<view class="section-header">
				<text class="section-title">基本信息</text>
				<text class="section-icon">👤</text>
			</view>
			<view class="form-card">
				<view class="form-item">
					<view class="form-label">
						<text class="label-icon">✏️</text>
						<text class="label-text">昵称</text>
					</view>
					<input 
						class="form-input" 
						v-model="userProfile.nickName" 
						placeholder="请输入昵称"
						maxlength="20"
					/>
				</view>
				<view class="form-item">
					<view class="form-label">
						<text class="label-icon">💭</text>
						<text class="label-text">个性签名</text>
					</view>
					<textarea 
						class="form-textarea" 
						v-model="userProfile.signature" 
						placeholder="写下你的个性签名..."
						maxlength="50"
					/>
				</view>
			</view>
		</view>

		<!-- 详细信息编辑 -->
		<view class="form-section">
			<view class="section-header">
				<text class="section-title">详细信息</text>
				<text class="section-icon">📋</text>
			</view>
			<view class="form-card">
				<view class="form-item" @tap="selectBirthday">
					<view class="form-label">
						<text class="label-icon">🎂</text>
						<text class="label-text">生日</text>
					</view>
					<view class="form-value">
						<text class="value-text">{{ userProfile.birthday || '请选择生日' }}</text>
						<text class="form-arrow">→</text>
					</view>
				</view>
				<view class="form-item" @tap="selectGender">
					<view class="form-label">
						<text class="label-icon">⚧️</text>
						<text class="label-text">性别</text>
					</view>
					<view class="form-value">
						<text class="value-text">{{ genderText }}</text>
						<text class="form-arrow">→</text>
					</view>
				</view>
				<view class="form-item" @tap="selectRegion">
					<view class="form-label">
						<text class="label-icon">📍</text>
						<text class="label-text">地区</text>
					</view>
					<view class="form-value">
						<text class="value-text">{{ userProfile.region || '请选择地区' }}</text>
						<text class="form-arrow">→</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 操作按钮 -->
		<view class="action-section">
			<button class="save-btn" @tap="saveProfile">
				<text class="save-text">保存修改</text>
			</button>
			<button class="cancel-btn" @tap="cancelEdit">
				<text class="cancel-text">取消</text>
			</button>
		</view>

		<!-- 日期选择器 -->
		<picker 
			v-if="showDatePicker"
			mode="date" 
			:value="userProfile.birthday"
			@change="onDateChange"
			@cancel="showDatePicker = false"
		>
			<view></view>
		</picker>
	</view>
</template>

<script>
export default {
	data() {
		return {
			userProfile: {
				avatarUrl: '',
				nickName: '',
				signature: '',
				birthday: '',
				gender: 0, // 0:未知 1:男 2:女
				region: ''
			},
			defaultAvatar: '/static/default-avatar.png',
			showDatePicker: false,
			originalProfile: {} // 保存原始数据用于取消操作
		};
	},
	computed: {
		genderText() {
			const genderMap = { 0: '未设置', 1: '男', 2: '女' };
			return genderMap[this.userProfile.gender] || '未设置';
		}
	},
	onLoad() {
		this.loadUserProfile();
	},
	methods: {
		// 加载用户资料
		loadUserProfile() {
			const userInfo = uni.getStorageSync('userInfo');
			if (userInfo) {
				this.userProfile = {
					avatarUrl: userInfo.avatarUrl || '',
					nickName: userInfo.nickName || '',
					signature: userInfo.signature || '',
					birthday: userInfo.birthday || '',
					gender: userInfo.gender || 0,
					region: userInfo.region || ''
				};
				// 保存原始数据
				this.originalProfile = JSON.parse(JSON.stringify(this.userProfile));
			}
		},

		// 更换头像
		changeAvatar() {
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					this.userProfile.avatarUrl = res.tempFilePaths[0];
					uni.showToast({
						title: '头像已更新',
						icon: 'success'
					});
				}
			});
		},

		// 选择生日
		selectBirthday() {
			this.showDatePicker = true;
		},

		// 日期改变
		onDateChange(e) {
			this.userProfile.birthday = e.detail.value;
			this.showDatePicker = false;
		},

		// 选择性别
		selectGender() {
			uni.showActionSheet({
				itemList: ['男', '女', '不设置'],
				success: (res) => {
					this.userProfile.gender = res.tapIndex === 2 ? 0 : res.tapIndex + 1;
				}
			});
		},

		// 选择地区
		selectRegion() {
			uni.showToast({
				title: '地区选择功能待完善',
				icon: 'none'
			});
		},

		// 保存资料
		saveProfile() {
			// 验证必填项
			if (!this.userProfile.nickName.trim()) {
				uni.showToast({
					title: '请输入昵称',
					icon: 'none'
				});
				return;
			}

			// 保存到本地存储
			const userInfo = uni.getStorageSync('userInfo') || {};
			const updatedUserInfo = {
				...userInfo,
				...this.userProfile
			};
			
			uni.setStorageSync('userInfo', updatedUserInfo);
			
			uni.showToast({
				title: '保存成功',
				icon: 'success'
			});

			setTimeout(() => {
				uni.navigateBack();
			}, 1500);
		},

		// 取消编辑
		cancelEdit() {
			// 检查是否有修改
			const hasChanges = JSON.stringify(this.userProfile) !== JSON.stringify(this.originalProfile);
			
			if (hasChanges) {
				uni.showModal({
					title: '确认取消',
					content: '您有未保存的修改，确定要取消吗？',
					success: (res) => {
						if (res.confirm) {
							uni.navigateBack();
						}
					}
				});
			} else {
				uni.navigateBack();
			}
		}
	}
};
</script>

<style scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
	padding: 30rpx;
	position: relative;
	overflow: hidden;
}

/* 背景装饰 */
.bg-decoration {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
	z-index: 1;
}

.decoration-circle {
	position: absolute;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.1);
	animation: float 6s ease-in-out infinite;
}

.circle-1 {
	width: 180rpx;
	height: 180rpx;
	top: 15%;
	right: -40rpx;
	animation-delay: 0s;
}

.circle-2 {
	width: 120rpx;
	height: 120rpx;
	top: 70%;
	left: -20rpx;
	animation-delay: 2s;
}

.circle-3 {
	width: 80rpx;
	height: 80rpx;
	top: 40%;
	left: 60%;
	animation-delay: 4s;
}

@keyframes float {
	0%, 100% {
		transform: translateY(0px) rotate(0deg);
	}
	50% {
		transform: translateY(-15rpx) rotate(180deg);
	}
}

/* 头像编辑区域 */
.avatar-section {
	position: relative;
	z-index: 2;
	margin-bottom: 30rpx;
	animation: slideUp 0.8s ease-out;
}

.avatar-card {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 30rpx;
	padding: 40rpx;
	text-align: center;
	box-shadow: 0 15rpx 35rpx rgba(255, 105, 180, 0.3);
	backdrop-filter: blur(15rpx);
}

.avatar-container {
	position: relative;
	display: inline-block;
	margin-bottom: 20rpx;
}

.profile-avatar {
	width: 200rpx;
	height: 200rpx;
	border-radius: 100rpx;
	border: 4rpx solid #ff69b4;
	position: relative;
	z-index: 2;
}

.avatar-border {
	position: absolute;
	top: -10rpx;
	left: -10rpx;
	width: 220rpx;
	height: 220rpx;
	border: 2rpx solid rgba(255, 105, 180, 0.3);
	border-radius: 110rpx;
	animation: rotate 4s linear infinite;
}

.camera-icon {
	position: absolute;
	bottom: 10rpx;
	right: 10rpx;
	width: 50rpx;
	height: 50rpx;
	background: #ff69b4;
	border-radius: 25rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	color: white;
	box-shadow: 0 4rpx 12rpx rgba(255, 105, 180, 0.4);
	z-index: 3;
}

.avatar-tip {
	font-size: 24rpx;
	color: #ff69b4;
	opacity: 0.8;
}

/* 表单区域 */
.form-section {
	position: relative;
	z-index: 2;
	margin-bottom: 30rpx;
	animation: slideUp 0.8s ease-out;
}

.form-section:nth-child(3) { animation-delay: 0.2s; }
.form-section:nth-child(4) { animation-delay: 0.4s; }

.section-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 15rpx;
	padding: 0 10rpx;
}

.section-title {
	font-size: 32rpx;
	color: #ff1493;
	font-weight: bold;
}

.section-icon {
	font-size: 28rpx;
}

.form-card {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 25rpx;
	backdrop-filter: blur(15rpx);
	box-shadow: 0 10rpx 30rpx rgba(255, 105, 180, 0.25);
	overflow: hidden;
}

.form-item {
	padding: 25rpx 30rpx;
	border-bottom: 1rpx solid rgba(255, 105, 180, 0.1);
	transition: all 0.3s ease;
}

.form-item:last-child {
	border-bottom: none;
}

.form-item:active {
	background: rgba(255, 105, 180, 0.05);
}

.form-label {
	display: flex;
	align-items: center;
	margin-bottom: 15rpx;
}

.label-icon {
	font-size: 28rpx;
	margin-right: 15rpx;
	width: 35rpx;
	text-align: center;
}

.label-text {
	font-size: 28rpx;
	color: #ff1493;
	font-weight: 500;
}

.form-input {
	width: 100%;
	font-size: 26rpx;
	color: #333;
	background: rgba(255, 255, 255, 0.8);
	border: 2rpx solid rgba(255, 105, 180, 0.2);
	border-radius: 15rpx;
	padding: 20rpx;
	box-sizing: border-box;
}

.form-input:focus {
	border-color: #ff69b4;
	background: rgba(255, 255, 255, 0.95);
}

.form-textarea {
	width: 100%;
	height: 120rpx;
	font-size: 26rpx;
	color: #333;
	background: rgba(255, 255, 255, 0.8);
	border: 2rpx solid rgba(255, 105, 180, 0.2);
	border-radius: 15rpx;
	padding: 20rpx;
	box-sizing: border-box;
	resize: none;
}

.form-textarea:focus {
	border-color: #ff69b4;
	background: rgba(255, 255, 255, 0.95);
}

.form-value {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx;
	background: rgba(255, 255, 255, 0.8);
	border: 2rpx solid rgba(255, 105, 180, 0.2);
	border-radius: 15rpx;
}

.value-text {
	font-size: 26rpx;
	color: #333;
	flex: 1;
}

.form-arrow {
	font-size: 20rpx;
	color: #ff69b4;
	opacity: 0.6;
}

/* 操作按钮 */
.action-section {
	position: relative;
	z-index: 2;
	animation: slideUp 0.8s ease-out 0.6s both;
}

.save-btn {
	width: 100%;
	background: linear-gradient(135deg, #ff69b4, #ff1493);
	border: none;
	border-radius: 25rpx;
	padding: 25rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 8rpx 20rpx rgba(255, 20, 147, 0.3);
	transition: all 0.3s ease;
}

.save-btn:active {
	transform: scale(0.98);
	box-shadow: 0 4rpx 15rpx rgba(255, 20, 147, 0.4);
}

.save-text {
	color: white;
	font-size: 28rpx;
	font-weight: 600;
}

.cancel-btn {
	width: 100%;
	background: rgba(255, 255, 255, 0.9);
	border: 2rpx solid #ff69b4;
	border-radius: 25rpx;
	padding: 25rpx;
	box-shadow: 0 8rpx 20rpx rgba(255, 105, 180, 0.2);
	backdrop-filter: blur(10rpx);
	transition: all 0.3s ease;
}

.cancel-btn:active {
	transform: scale(0.98);
	background: rgba(255, 105, 180, 0.1);
}

.cancel-text {
	color: #ff1493;
	font-size: 28rpx;
	font-weight: 600;
}

/* 动画效果 */
@keyframes slideUp {
	0% {
		transform: translateY(50rpx);
		opacity: 0;
	}
	100% {
		transform: translateY(0);
		opacity: 1;
	}
}

@keyframes rotate {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}
</style>
