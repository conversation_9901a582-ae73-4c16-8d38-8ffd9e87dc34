<template>
	<view class="edit-profile-container">
		<!-- 页面标题 -->
		<view class="page-header">
			<text class="page-title">编辑资料</text>
		</view>
		
		<!-- 头像编辑区域 -->
		<view class="avatar-section">
			<view class="avatar-container">
				<image class="profile-avatar" src="/static/logo.png" mode="aspectFill"></image>
				<view class="avatar-edit-overlay">
					<view class="camera-icon">📷</view>
				</view>
			</view>
			<text class="avatar-tip">点击更换头像</text>
		</view>
		
		<!-- 基本信息表单 -->
		<view class="form-section">
			<text class="section-title">基本信息</text>
			<view class="form-list">
				<view class="form-item">
					<text class="form-label">昵称</text>
					<input 
						class="form-input" 
						placeholder="请输入昵称" 
						value="甜蜜情侣"
						maxlength="20"
					/>
				</view>
				<view class="form-item">
					<text class="form-label">性别</text>
					<view class="gender-selector">
						<view class="gender-option active">
							<text class="gender-text">女生</text>
						</view>
						<view class="gender-option">
							<text class="gender-text">男生</text>
						</view>
					</view>
				</view>
				<view class="form-item">
					<text class="form-label">生日</text>
					<picker mode="date" value="1995-01-01">
						<view class="picker-wrapper">
							<text class="picker-text">1995-01-01</text>
							<text class="picker-arrow">></text>
						</view>
					</picker>
				</view>
				<view class="form-item">
					<text class="form-label">地区</text>
					<picker mode="region" value="['广东省', '深圳市', '南山区']">
						<view class="picker-wrapper">
							<text class="picker-text">广东 深圳 南山</text>
							<text class="picker-arrow">></text>
						</view>
					</picker>
				</view>
			</view>
		</view>
		
		<!-- 个性签名 -->
		<view class="form-section">
			<text class="section-title">个性签名</text>
			<view class="signature-wrapper">
				<textarea 
					class="signature-textarea" 
					placeholder="写下你的个性签名吧~" 
					maxlength="50"
					value="愿得一人心，白首不分离 💕"
				></textarea>
				<text class="char-counter">25/50</text>
			</view>
		</view>
		
		<!-- 情侣信息 -->
		<view class="form-section">
			<text class="section-title">情侣信息</text>
			<view class="form-list">
				<view class="form-item">
					<text class="form-label">恋爱纪念日</text>
					<picker mode="date" value="2023-02-14">
						<view class="picker-wrapper">
							<text class="picker-text">2023-02-14</text>
							<text class="picker-arrow">></text>
						</view>
					</picker>
				</view>
				<view class="form-item">
					<text class="form-label">对TA的称呼</text>
					<input 
						class="form-input" 
						placeholder="给TA起个昵称" 
						value="小宝贝"
						maxlength="10"
					/>
				</view>
				<view class="form-item">
					<text class="form-label">关系状态</text>
					<picker range="['热恋中', '稳定期', '蜜月期', '磨合期']" value="0">
						<view class="picker-wrapper">
							<text class="picker-text">热恋中</text>
							<text class="picker-arrow">></text>
						</view>
					</picker>
				</view>
			</view>
		</view>
		
		<!-- 保存按钮 -->
		<view class="save-section">
			<button class="save-btn" @tap="handleSave">
				保存修改
			</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			}
		},
		methods: {
			handleSave() {
				uni.showToast({
					title: '保存成功',
					icon: 'success'
				});
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			}
		}
	}
</script>

<style scoped>
	.edit-profile-container {
		min-height: 100vh;
		background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
		padding: 40rpx 30rpx;
	}
	
	/* 页面标题 */
	.page-header {
		text-align: center;
		margin-bottom: 40rpx;
	}
	
	.page-title {
		font-size: 40rpx;
		font-weight: 700;
		color: #fff;
		text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);
	}
	
	/* 头像编辑 */
	.avatar-section {
		text-align: center;
		background: rgba(255,255,255,0.95);
		border-radius: 24rpx;
		padding: 60rpx 40rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 8rpx 24rpx rgba(255,154,158,0.2);
		backdrop-filter: blur(10rpx);
	}
	
	.avatar-container {
		position: relative;
		display: inline-block;
		margin-bottom: 20rpx;
	}
	
	.profile-avatar {
		width: 160rpx;
		height: 160rpx;
		border-radius: 80rpx;
		border: 4rpx solid #ff6b9d;
	}
	
	.avatar-edit-overlay {
		position: absolute;
		bottom: -8rpx;
		right: -8rpx;
		width: 48rpx;
		height: 48rpx;
		background: #ff6b9d;
		border-radius: 24rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 4rpx 12rpx rgba(255,107,157,0.3);
	}
	
	.camera-icon {
		font-size: 20rpx;
		color: #fff;
	}
	
	.avatar-tip {
		font-size: 24rpx;
		color: #999;
	}
	
	/* 表单区域 */
	.form-section {
		margin-bottom: 30rpx;
	}
	
	.section-title {
		font-size: 28rpx;
		font-weight: 600;
		color: rgba(255,255,255,0.9);
		margin-bottom: 20rpx;
		margin-left: 20rpx;
		display: block;
	}
	
	.form-list {
		background: rgba(255,255,255,0.95);
		border-radius: 24rpx;
		overflow: hidden;
		box-shadow: 0 8rpx 24rpx rgba(255,154,158,0.2);
		backdrop-filter: blur(10rpx);
	}
	
	.form-item {
		display: flex;
		align-items: center;
		padding: 40rpx;
		border-bottom: 1rpx solid rgba(0,0,0,0.05);
	}
	
	.form-item:last-child {
		border-bottom: none;
	}
	
	.form-label {
		width: 160rpx;
		font-size: 30rpx;
		color: #333;
		font-weight: 500;
	}
	
	.form-input {
		flex: 1;
		font-size: 30rpx;
		color: #333;
		text-align: right;
		background: transparent;
		border: none;
	}
	
	.form-input::placeholder {
		color: #ccc;
	}
	
	/* 性别选择器 */
	.gender-selector {
		flex: 1;
		display: flex;
		justify-content: flex-end;
		gap: 16rpx;
	}
	
	.gender-option {
		padding: 16rpx 28rpx;
		border-radius: 40rpx;
		border: 2rpx solid rgba(255,107,157,0.3);
		transition: all 0.3s ease;
	}
	
	.gender-option.active {
		background: #ff6b9d;
		border-color: #ff6b9d;
	}
	
	.gender-text {
		font-size: 26rpx;
		color: #666;
	}
	
	.gender-option.active .gender-text {
		color: #fff;
	}
	
	/* 选择器 */
	.picker-wrapper {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: flex-end;
	}
	
	.picker-text {
		font-size: 30rpx;
		color: #333;
		margin-right: 12rpx;
	}
	
	.picker-arrow {
		font-size: 24rpx;
		color: #ccc;
	}
	
	/* 个性签名 */
	.signature-wrapper {
		background: rgba(255,255,255,0.95);
		border-radius: 24rpx;
		padding: 40rpx;
		box-shadow: 0 8rpx 24rpx rgba(255,154,158,0.2);
		backdrop-filter: blur(10rpx);
	}
	
	.signature-textarea {
		width: 100%;
		min-height: 120rpx;
		font-size: 30rpx;
		color: #333;
		line-height: 1.6;
		background: rgba(255,107,157,0.05);
		border-radius: 16rpx;
		padding: 24rpx;
		border: 1rpx solid rgba(255,107,157,0.1);
		margin-bottom: 16rpx;
	}
	
	.signature-textarea::placeholder {
		color: #ccc;
	}
	
	.char-counter {
		text-align: right;
		font-size: 24rpx;
		color: #999;
		display: block;
	}
	
	/* 保存按钮 */
	.save-section {
		margin-top: 60rpx;
		text-align: center;
	}
	
	.save-btn {
		width: 100%;
		background: linear-gradient(135deg, #ff6b9d, #c44569);
		color: #fff;
		border: none;
		border-radius: 50rpx;
		padding: 36rpx;
		font-size: 32rpx;
		font-weight: 600;
		box-shadow: 0 8rpx 24rpx rgba(196,69,105,0.3);
		transition: all 0.3s ease;
	}
	
	.save-btn:active {
		transform: translateY(2rpx);
		box-shadow: 0 6rpx 20rpx rgba(196,69,105,0.4);
	}
</style>