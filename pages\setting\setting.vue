<template>
  <view class="container">
    <!-- 背景装饰 -->
    <view class="bg-decoration">
      <view class="decoration-circle circle-1"></view>
      <view class="decoration-circle circle-2"></view>
      <view class="decoration-circle circle-3"></view>
    </view>

    <!-- 个人设置 -->
    <view class="setting-section">
      <view class="section-header">
        <text class="section-title">个人设置</text>
        <text class="section-icon">👤</text>
      </view>
      <view class="setting-card">
        <view class="setting-item" @click="editProfile">
          <view class="setting-left">
            <text class="setting-icon">📝</text>
            <text class="setting-label">编辑资料</text>
          </view>
          <view class="setting-right">
            <text class="setting-value">修改头像、昵称</text>
            <text class="setting-arrow">→</text>
          </view>
        </view>
        <view class="setting-item" @tap="accountSecurity">
          <view class="setting-left">
            <text class="setting-icon">🔒</text>
            <text class="setting-label">账号与安全</text>
          </view>
          <view class="setting-right">
            <text class="setting-value">密码、绑定</text>
            <text class="setting-arrow">→</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 情侣关系设置 -->
    <view class="setting-section">
      <view class="section-header">
        <text class="section-title">情侣关系</text>
        <text class="section-icon">💕</text>
      </view>
      <view class="setting-card">
        <view class="setting-item" @tap="coupleConnection">
          <view class="setting-left">
            <text class="setting-icon">🔗</text>
            <text class="setting-label">情侣连接</text>
          </view>
          <view class="setting-right">
            <text class="setting-value">{{ coupleStatus }}</text>
            <text class="setting-arrow">→</text>
          </view>
        </view>
        <view class="setting-item" @tap="setLoveDate">
          <view class="setting-left">
            <text class="setting-icon">📅</text>
            <text class="setting-label">恋爱开始日期</text>
          </view>
          <view class="setting-right">
            <text class="setting-value">{{ loveStartDate }}</text>
            <text class="setting-arrow">→</text>
          </view>
        </view>
        <view class="setting-item" @tap="setCoupleNickname">
          <view class="setting-left">
            <text class="setting-icon">💝</text>
            <text class="setting-label">情侣昵称</text>
          </view>
          <view class="setting-right">
            <text class="setting-value">设置专属称呼</text>
            <text class="setting-arrow">→</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 隐私设置 -->
    <view class="setting-section">
      <view class="section-header">
        <text class="section-title">隐私设置</text>
        <text class="section-icon">🛡️</text>
      </view>
      <view class="setting-card">
        <view class="setting-item">
          <view class="setting-left">
            <text class="setting-icon">👁️</text>
            <text class="setting-label">动态可见性</text>
          </view>
          <view class="setting-right">
            <switch :checked="privacySettings.dynamicVisible" @change="toggleDynamicVisible" color="#ff69b4" />
          </view>
        </view>
        <view class="setting-item">
          <view class="setting-left">
            <text class="setting-icon">📍</text>
            <text class="setting-label">位置共享</text>
          </view>
          <view class="setting-right">
            <switch :checked="privacySettings.locationShare" @change="toggleLocationShare" color="#ff69b4" />
          </view>
        </view>
        <view class="setting-item">
          <view class="setting-left">
            <text class="setting-icon">🟢</text>
            <text class="setting-label">在线状态显示</text>
          </view>
          <view class="setting-right">
            <switch :checked="privacySettings.onlineStatus" @change="toggleOnlineStatus" color="#ff69b4" />
          </view>
        </view>
      </view>
    </view>

    <!-- 通知设置 -->
    <view class="setting-section">
      <view class="section-header">
        <text class="section-title">通知设置</text>
        <text class="section-icon">🔔</text>
      </view>
      <view class="setting-card">
        <view class="setting-item">
          <view class="setting-left">
            <text class="setting-icon">💬</text>
            <text class="setting-label">消息通知</text>
          </view>
          <view class="setting-right">
            <switch :checked="notificationSettings.message" @change="toggleMessageNotification" color="#ff69b4" />
          </view>
        </view>
        <view class="setting-item">
          <view class="setting-left">
            <text class="setting-icon">🎉</text>
            <text class="setting-label">纪念日提醒</text>
          </view>
          <view class="setting-right">
            <switch :checked="notificationSettings.anniversary" @change="toggleAnniversaryNotification" color="#ff69b4" />
          </view>
        </view>
        <view class="setting-item">
          <view class="setting-left">
            <text class="setting-icon">💌</text>
            <text class="setting-label">每日情话</text>
          </view>
          <view class="setting-right">
            <switch :checked="notificationSettings.dailyQuote" @change="toggleDailyQuoteNotification" color="#ff69b4" />
          </view>
        </view>
        <view class="setting-item">
          <view class="setting-left">
            <text class="setting-icon">📢</text>
            <text class="setting-label">系统通知</text>
          </view>
          <view class="setting-right">
            <switch :checked="notificationSettings.system" @change="toggleSystemNotification" color="#ff69b4" />
          </view>
        </view>
      </view>
    </view>

    <!-- 应用设置 -->
    <view class="setting-section">
      <view class="section-header">
        <text class="section-title">应用设置</text>
        <text class="section-icon">⚙️</text>
      </view>
      <view class="setting-card">
        <view class="setting-item" @tap="setTheme">
          <view class="setting-left">
            <text class="setting-icon">🎨</text>
            <text class="setting-label">主题设置</text>
          </view>
          <view class="setting-right">
            <text class="setting-value">{{ currentTheme }}</text>
            <text class="setting-arrow">→</text>
          </view>
        </view>
        <view class="setting-item" @tap="setFontSize">
          <view class="setting-left">
            <text class="setting-icon">🔤</text>
            <text class="setting-label">字体大小</text>
          </view>
          <view class="setting-right">
            <text class="setting-value">{{ fontSize }}</text>
            <text class="setting-arrow">→</text>
          </view>
        </view>
        <view class="setting-item" @tap="clearCache">
          <view class="setting-left">
            <text class="setting-icon">🗑️</text>
            <text class="setting-label">清除缓存</text>
          </view>
          <view class="setting-right">
            <text class="setting-value">{{ cacheSize }}</text>
            <text class="setting-arrow">→</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 关于与帮助 -->
    <view class="setting-section">
      <view class="section-header">
        <text class="section-title">关于与帮助</text>
        <text class="section-icon">❓</text>
      </view>
      <view class="setting-card">
        <view class="setting-item" @tap="checkVersion">
          <view class="setting-left">
            <text class="setting-icon">📱</text>
            <text class="setting-label">版本信息</text>
          </view>
          <view class="setting-right">
            <text class="setting-value">v{{ appVersion }}</text>
            <text class="setting-arrow">→</text>
          </view>
        </view>
        <view class="setting-item" @tap="userAgreement">
          <view class="setting-left">
            <text class="setting-icon">📄</text>
            <text class="setting-label">用户协议</text>
          </view>
          <view class="setting-right">
            <text class="setting-arrow">→</text>
          </view>
        </view>
        <view class="setting-item" @tap="privacyPolicy">
          <view class="setting-left">
            <text class="setting-icon">🔐</text>
            <text class="setting-label">隐私政策</text>
          </view>
          <view class="setting-right">
            <text class="setting-arrow">→</text>
          </view>
        </view>
        <view class="setting-item" @tap="feedback">
          <view class="setting-left">
            <text class="setting-icon">💭</text>
            <text class="setting-label">意见反馈</text>
          </view>
          <view class="setting-right">
            <text class="setting-arrow">→</text>
          </view>
        </view>
        <view class="setting-item" @tap="contactService">
          <view class="setting-left">
            <text class="setting-icon">🎧</text>
            <text class="setting-label">联系客服</text>
          </view>
          <view class="setting-right">
            <text class="setting-arrow">→</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 退出登录 -->
    <view class="logout-section">
      <button class="logout-btn" @tap="logout">
        <text class="logout-text">退出登录</text>
      </button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      coupleStatus: '已连接',
      loveStartDate: '2023-08-04',
      currentTheme: '浪漫粉色',
      fontSize: '标准',
      cacheSize: '12.5MB',
      appVersion: '1.0.0',
      privacySettings: {
        dynamicVisible: true,
        locationShare: false,
        onlineStatus: true
      },
      notificationSettings: {
        message: true,
        anniversary: true,
        dailyQuote: true,
        system: false
      }
    };
  },
  methods: {
    // 个人设置
    editProfile() {
		 if(uni.getStorageSync('userInfo')){
		    uni.navigateTo({
		      url:'/pages/profile/edit-profile'
		    })
		  }else{  // 修正：将 })else 改为 }else
		    uni.navigateTo({
		      url:'/pages/index/index'
		    })
		  }
    },
    accountSecurity() {
      uni.showToast({ title: '账号与安全功能待开发', icon: 'none' });
    },

    // 情侣关系设置
    coupleConnection() {
      uni.showToast({ title: '情侣连接功能待开发', icon: 'none' });
    },
    setLoveDate() {
      uni.showToast({ title: '设置恋爱日期功能待开发', icon: 'none' });
    },
    setCoupleNickname() {
      uni.showToast({ title: '设置情侣昵称功能待开发', icon: 'none' });
    },

    // 隐私设置开关
    toggleDynamicVisible(e) {
      this.privacySettings.dynamicVisible = e.detail.value;
      uni.showToast({
        title: e.detail.value ? '已开启动态可见' : '已关闭动态可见',
        icon: 'none'
      });
    },
    toggleLocationShare(e) {
      this.privacySettings.locationShare = e.detail.value;
      uni.showToast({
        title: e.detail.value ? '已开启位置共享' : '已关闭位置共享',
        icon: 'none'
      });
    },
    toggleOnlineStatus(e) {
      this.privacySettings.onlineStatus = e.detail.value;
      uni.showToast({
        title: e.detail.value ? '已显示在线状态' : '已隐藏在线状态',
        icon: 'none'
      });
    },

    // 通知设置开关
    toggleMessageNotification(e) {
      this.notificationSettings.message = e.detail.value;
      uni.showToast({
        title: e.detail.value ? '已开启消息通知' : '已关闭消息通知',
        icon: 'none'
      });
    },
    toggleAnniversaryNotification(e) {
      this.notificationSettings.anniversary = e.detail.value;
      uni.showToast({
        title: e.detail.value ? '已开启纪念日提醒' : '已关闭纪念日提醒',
        icon: 'none'
      });
    },
    toggleDailyQuoteNotification(e) {
      this.notificationSettings.dailyQuote = e.detail.value;
      uni.showToast({
        title: e.detail.value ? '已开启每日情话' : '已关闭每日情话',
        icon: 'none'
      });
    },
    toggleSystemNotification(e) {
      this.notificationSettings.system = e.detail.value;
      uni.showToast({
        title: e.detail.value ? '已开启系统通知' : '已关闭系统通知',
        icon: 'none'
      });
    },

    // 应用设置
    setTheme() {
      uni.showActionSheet({
        itemList: ['浪漫粉色', '清新蓝色', '温暖橙色', '优雅紫色'],
        success: (res) => {
          const themes = ['浪漫粉色', '清新蓝色', '温暖橙色', '优雅紫色'];
          this.currentTheme = themes[res.tapIndex];
          uni.showToast({ title: `已切换到${this.currentTheme}主题`, icon: 'none' });
        }
      });
    },
    setFontSize() {
      uni.showActionSheet({
        itemList: ['小', '标准', '大', '超大'],
        success: (res) => {
          const sizes = ['小', '标准', '大', '超大'];
          this.fontSize = sizes[res.tapIndex];
          uni.showToast({ title: `字体大小已设置为${this.fontSize}`, icon: 'none' });
        }
      });
    },
    clearCache() {
      uni.showModal({
        title: '清除缓存',
        content: '确定要清除所有缓存数据吗？',
        success: (res) => {
          if (res.confirm) {
            this.cacheSize = '0MB';
            uni.showToast({ title: '缓存清除成功', icon: 'success' });
          }
        }
      });
    },

    // 关于与帮助
    checkVersion() {
      uni.showToast({ title: '已是最新版本', icon: 'success' });
    },
    userAgreement() {
      uni.showToast({ title: '用户协议功能待开发', icon: 'none' });
    },
    privacyPolicy() {
      uni.showToast({ title: '隐私政策功能待开发', icon: 'none' });
    },
    feedback() {
      uni.showToast({ title: '意见反馈功能待开发', icon: 'none' });
    },
    contactService() {
      uni.showToast({ title: '联系客服功能待开发', icon: 'none' });
    },

    // 退出登录
    logout() {
      uni.showModal({
        title: '确认退出',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({ title: '已退出登录', icon: 'success' });
            setTimeout(() => {
              uni.switchTab({ url: '/pages/index/index' });
            }, 1500);
          }
        }
      });
    }
  }
};
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  padding: 30rpx;
  position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.bg-decoration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 180rpx;
  height: 180rpx;
  top: 15%;
  right: -40rpx;
  animation-delay: 0s;
}

.circle-2 {
  width: 120rpx;
  height: 120rpx;
  top: 70%;
  left: -20rpx;
  animation-delay: 2s;
}

.circle-3 {
  width: 80rpx;
  height: 80rpx;
  top: 40%;
  left: 60%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-15rpx) rotate(180deg);
  }
}

/* 设置区域 */
.setting-section {
  position: relative;
  z-index: 2;
  margin-bottom: 30rpx;
  animation: slideUp 0.8s ease-out;
}

.setting-section:nth-child(2) { animation-delay: 0.1s; }
.setting-section:nth-child(3) { animation-delay: 0.2s; }
.setting-section:nth-child(4) { animation-delay: 0.3s; }
.setting-section:nth-child(5) { animation-delay: 0.4s; }
.setting-section:nth-child(6) { animation-delay: 0.5s; }
.setting-section:nth-child(7) { animation-delay: 0.6s; }

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15rpx;
  padding: 0 10rpx;
}

.section-title {
  font-size: 32rpx;
  color: #ff1493;
  font-weight: bold;
}

.section-icon {
  font-size: 28rpx;
}

.setting-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 25rpx;
  backdrop-filter: blur(15rpx);
  box-shadow: 0 10rpx 30rpx rgba(255, 105, 180, 0.25);
  overflow: hidden;
}

/* 设置项 */
.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid rgba(255, 105, 180, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item:active {
  background: rgba(255, 105, 180, 0.05);
  transform: scale(0.98);
}

.setting-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.setting-item:active::before {
  left: 100%;
}

.setting-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.setting-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
  width: 40rpx;
  text-align: center;
}

.setting-label {
  font-size: 28rpx;
  color: #ff1493;
  font-weight: 500;
}

.setting-right {
  display: flex;
  align-items: center;
}

.setting-value {
  font-size: 24rpx;
  color: #ff69b4;
  opacity: 0.8;
  margin-right: 10rpx;
}

.setting-arrow {
  font-size: 20rpx;
  color: #ff69b4;
  opacity: 0.6;
}

/* 退出登录 */
.logout-section {
  position: relative;
  z-index: 2;
  animation: slideUp 0.8s ease-out 0.7s both;
}

.logout-btn {
  width: 100%;
  background: rgba(255, 255, 255, 0.9);
  border: 2rpx solid #ff69b4;
  border-radius: 25rpx;
  padding: 25rpx;
  box-shadow: 0 8rpx 20rpx rgba(255, 105, 180, 0.2);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.logout-btn:active {
  transform: scale(0.98);
  background: rgba(255, 105, 180, 0.1);
  box-shadow: 0 4rpx 15rpx rgba(255, 105, 180, 0.3);
}

.logout-text {
  color: #ff1493;
  font-size: 28rpx;
  font-weight: 600;
}

/* 动画效果 */
@keyframes slideUp {
  0% {
    transform: translateY(50rpx);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}
</style>
