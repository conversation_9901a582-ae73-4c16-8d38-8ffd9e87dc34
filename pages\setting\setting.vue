<template>
	<view class="setting-container">
		<!-- 页面标题 -->
		<view class="page-header">
			<text class="page-title">设置</text>
		</view>
		
		<!-- 账户信息卡片 -->
		<view class="account-card">
			<view class="account-info">
				<image class="account-avatar" src="/static/logo.png" mode="aspectFill"></image>
				<view class="account-details">
					<text class="account-name">甜蜜情侣</text>
					<text class="account-phone">138****8888</text>
				</view>
				<view class="vip-badge">VIP</view>
			</view>
		</view>
		
		<!-- 设置分组 -->
		<view class="setting-group">
			<text class="group-title">账户设置</text>
			<view class="setting-list">
				<view class="setting-item">
					<view class="item-left">
						<view class="item-icon">👤</view>
						<text class="item-text">个人资料</text>
					</view>
					<view class="item-right">
						<text class="item-arrow">></text>
					</view>
				</view>
				<view class="setting-item">
					<view class="item-left">
						<view class="item-icon">🔒</view>
						<text class="item-text">隐私设置</text>
					</view>
					<view class="item-right">
						<text class="item-arrow">></text>
					</view>
				</view>
				<view class="setting-item">
					<view class="item-left">
						<view class="item-icon">🔑</view>
						<text class="item-text">修改密码</text>
					</view>
					<view class="item-right">
						<text class="item-arrow">></text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 应用设置 -->
		<view class="setting-group">
			<text class="group-title">应用设置</text>
			<view class="setting-list">
				<view class="setting-item">
					<view class="item-left">
						<view class="item-icon">🔔</view>
						<text class="item-text">消息通知</text>
					</view>
					<view class="item-right">
						<switch class="setting-switch" checked="true" color="#ff6b9d" />
					</view>
				</view>
				<view class="setting-item">
					<view class="item-left">
						<view class="item-icon">🌙</view>
						<text class="item-text">夜间模式</text>
					</view>
					<view class="item-right">
						<switch class="setting-switch" color="#ff6b9d" />
					</view>
				</view>
				<view class="setting-item">
					<view class="item-left">
						<view class="item-icon">🔊</view>
						<text class="item-text">声音效果</text>
					</view>
					<view class="item-right">
						<switch class="setting-switch" checked="true" color="#ff6b9d" />
					</view>
				</view>
			</view>
		</view>
		
		<!-- 情侣设置 -->
		<view class="setting-group">
			<text class="group-title">情侣设置</text>
			<view class="setting-list">
				<view class="setting-item">
					<view class="item-left">
						<view class="item-icon">💕</view>
						<text class="item-text">恋爱纪念日</text>
					</view>
					<view class="item-right">
						<text class="item-value">2023-02-14</text>
						<text class="item-arrow">></text>
					</view>
				</view>
				<view class="setting-item">
					<view class="item-left">
						<view class="item-icon">🎂</view>
						<text class="item-text">生日提醒</text>
					</view>
					<view class="item-right">
						<switch class="setting-switch" checked="true" color="#ff6b9d" />
					</view>
				</view>
				<view class="setting-item">
					<view class="item-left">
						<view class="item-icon">📱</view>
						<text class="item-text">数据同步</text>
					</view>
					<view class="item-right">
						<text class="item-arrow">></text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 其他设置 -->
		<view class="setting-group">
			<text class="group-title">其他</text>
			<view class="setting-list">
				<view class="setting-item">
					<view class="item-left">
						<view class="item-icon">📋</view>
						<text class="item-text">用户协议</text>
					</view>
					<view class="item-right">
						<text class="item-arrow">></text>
					</view>
				</view>
				<view class="setting-item">
					<view class="item-left">
						<view class="item-icon">🛡️</view>
						<text class="item-text">隐私政策</text>
					</view>
					<view class="item-right">
						<text class="item-arrow">></text>
					</view>
				</view>
				<view class="setting-item">
					<view class="item-left">
						<view class="item-icon">ℹ️</view>
						<text class="item-text">关于我们</text>
					</view>
					<view class="item-right">
						<text class="item-value">v1.0.0</text>
						<text class="item-arrow">></text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 退出登录 -->
		<view class="logout-section">
			<button class="logout-btn" @tap="handleLogout">
				退出登录
			</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			}
		},
		methods: {
			handleLogout() {
				uni.showModal({
					title: '确认退出',
					content: '确定要退出登录吗？',
					success: (res) => {
						if (res.confirm) {
							uni.showToast({
								title: '已退出登录',
								icon: 'success'
							});
							setTimeout(() => {
								uni.reLaunch({
									url: '/pages/login/login'
								});
							}, 1500);
						}
					}
				});
			}
		}
	}
</script>

<style scoped>
	.setting-container {
		min-height: 100vh;
		background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
		padding: 40rpx 30rpx;
	}
	
	/* 页面标题 */
	.page-header {
		text-align: center;
		margin-bottom: 40rpx;
	}
	
	.page-title {
		font-size: 40rpx;
		font-weight: 700;
		color: #fff;
		text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);
	}
	
	/* 账户信息卡片 */
	.account-card {
		background: rgba(255,255,255,0.95);
		border-radius: 24rpx;
		padding: 40rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 8rpx 24rpx rgba(255,154,158,0.2);
		backdrop-filter: blur(10rpx);
	}
	
	.account-info {
		display: flex;
		align-items: center;
	}
	
	.account-avatar {
		width: 100rpx;
		height: 100rpx;
		border-radius: 50rpx;
		border: 3rpx solid #ff6b9d;
		margin-right: 30rpx;
	}
	
	.account-details {
		flex: 1;
	}
	
	.account-name {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		display: block;
		margin-bottom: 8rpx;
	}
	
	.account-phone {
		font-size: 26rpx;
		color: #999;
	}
	
	.vip-badge {
		background: linear-gradient(135deg, #ffd700, #ffb347);
		color: #fff;
		font-size: 20rpx;
		font-weight: 600;
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		box-shadow: 0 4rpx 12rpx rgba(255,215,0,0.3);
	}
	
	/* 设置分组 */
	.setting-group {
		margin-bottom: 30rpx;
	}
	
	.group-title {
		font-size: 28rpx;
		font-weight: 600;
		color: rgba(255,255,255,0.9);
		margin-bottom: 20rpx;
		margin-left: 20rpx;
		display: block;
	}
	
	.setting-list {
		background: rgba(255,255,255,0.95);
		border-radius: 24rpx;
		overflow: hidden;
		box-shadow: 0 8rpx 24rpx rgba(255,154,158,0.2);
		backdrop-filter: blur(10rpx);
	}
	
	.setting-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 40rpx;
		border-bottom: 1rpx solid rgba(0,0,0,0.05);
		transition: background-color 0.3s ease;
	}
	
	.setting-item:last-child {
		border-bottom: none;
	}
	
	.setting-item:active {
		background-color: rgba(255,107,157,0.05);
	}
	
	.item-left {
		display: flex;
		align-items: center;
		flex: 1;
	}
	
	.item-icon {
		font-size: 32rpx;
		margin-right: 24rpx;
		width: 40rpx;
		text-align: center;
	}
	
	.item-text {
		font-size: 30rpx;
		color: #333;
		font-weight: 500;
	}
	
	.item-right {
		display: flex;
		align-items: center;
	}
	
	.item-value {
		font-size: 26rpx;
		color: #999;
		margin-right: 16rpx;
	}
	
	.item-arrow {
		font-size: 24rpx;
		color: #ccc;
	}
	
	.setting-switch {
		transform: scale(0.8);
	}
	
	/* 退出登录 */
	.logout-section {
		margin-top: 60rpx;
		text-align: center;
	}
	
	.logout-btn {
		width: 100%;
		background: rgba(255,255,255,0.95);
		color: #ff6b9d;
		border: 2rpx solid #ff6b9d;
		border-radius: 50rpx;
		padding: 32rpx;
		font-size: 30rpx;
		font-weight: 600;
		box-shadow: 0 6rpx 20rpx rgba(255,154,158,0.15);
		transition: all 0.3s ease;
	}
	
	.logout-btn:active {
		background: rgba(255,107,157,0.1);
		transform: translateY(2rpx);
	}
</style>