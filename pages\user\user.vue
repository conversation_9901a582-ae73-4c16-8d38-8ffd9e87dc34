<template>
	<view class="user-container">
		<!-- 个人信息卡片 -->
		<view class="profile-card">
			<view class="profile-header">
				<view class="avatar-section">
					<image class="user-avatar" src="" mode="aspectFill"></image>
					<view class="online-dot"></view>
				</view>
				<view class="user-info">
					<text class="username">{{userInfo.nickName}}</text>
					<text class="user-status">在线</text>
				</view>
				<button class="edit-btn" @tap="navigateTo('/pages/profile/edit-profile')">
					编辑
				</button>
			</view>
		</view>
		
		<!-- 情侣关系卡片 -->
		<view class="relationship-card">
			<view class="relationship-header">
				<text class="card-title">我们的关系</text>
				<text class="love-status">热恋中 💕</text>
			</view>
			<view class="couple-info">
				<view class="couple-avatar-section">
					<image class="couple-avatar" src="/static/logo.png" mode="aspectFill"></image>
					<text class="couple-name">小甜心</text>
				</view>
				<view class="love-connection">
					<view class="love-line"></view>
					<view class="love-heart">💖</view>
				</view>
				<view class="couple-avatar-section">
					<image class="couple-avatar" src="/static/logo.png" mode="aspectFill"></image>
					<text class="couple-name">小宝贝</text>
				</view>
			</view>
			<view class="love-stats">
				<view class="stat-item">
					<text class="stat-number">520</text>
					<text class="stat-label">相恋天数</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">1314</text>
					<text class="stat-label">甜蜜时刻</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">99+</text>
					<text class="stat-label">共同回忆</text>
				</view>
			</view>
		</view>
		
		<!-- 功能菜单 */
		<view class="menu-grid">
			<view class="menu-item" @tap="navigateTo('/pages/profile/edit-profile')">
				<view class="menu-icon">👤</view>
				<text class="menu-text">编辑资料</text>
			</view>
			<view class="menu-item">
				<view class="menu-icon">📷</view>
				<text class="menu-text">相册</text>
			</view>
			<view class="menu-item">
				<view class="menu-icon">💌</view>
				<text class="menu-text">情书</text>
			</view>
			<view class="menu-item">
				<view class="menu-icon">🎂</view>
				<text class="menu-text">纪念日</text>
			</view>
			<view class="menu-item">
				<view class="menu-icon">🎁</view>
				<text class="menu-text">礼物</text>
			</view>
			<view class="menu-item" @tap="navigateTo('/pages/setting/setting')">
				<view class="menu-icon">⚙️</view>
				<text class="menu-text">设置</text>
			</view>
		</view>
		
		<!-- 退出登录 -->
		<view class="logout-section">
			<button class="logout-btn" @tap="handleLogout">
				退出登录
			</button>
		</view>
	</view>
</template>

<script>

	export default {
		data() {
			return {
				userInfo:null,
				isLogin:false
			}
		},
		methods: {
			//退出登录
			handleLogout() {
				uni.showModal({
				title:"退出登录",
				content: '请确认是否要退出?',
				success:(res)=>{
					if(res.confirm){
						uni.removeStorageSync('userInfo')
						uni.showToast({
							title:'已退出登录',
							icon:'success',
						})
						uni.navigateTo({
							url:"/pages/login/login"
						})
					}
				},fail:(err)=>{
					uni.showToast({
						title:'退出登录失败',
						icon:'none',
					})
				}
				})

				
			},
			//检查登录状态
			checkLoginStatus() {
			            const userInfo = uni.getStorageSync('userInfo');
			            if (userInfo) {
			                this.userInfo = userInfo;
			                this.isLogin = true;
			            } else {
			                this.userInfo = null;
			                this.isLogin = false;
			            }
			        }
			  
		},
		onShow(){
			this.checkLoginStatus()
		}
	}
</script>

<style scoped>
	.user-container {
		min-height: 100vh;
		background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
		padding: 40rpx 30rpx;
	}
	
	/* 个人信息卡片 */
	.profile-card {
		background: rgba(255,255,255,0.95);
		border-radius: 24rpx;
		padding: 40rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 8rpx 24rpx rgba(255,154,158,0.2);
		backdrop-filter: blur(10rpx);
	}
	
	.profile-header {
		display: flex;
		align-items: center;
	}
	
	.avatar-section {
		position: relative;
		margin-right: 30rpx;
	}
	
	.user-avatar {
		width: 120rpx;
		height: 120rpx;
		border-radius: 60rpx;
		border: 4rpx solid #ff6b9d;
	}
	
	.online-dot {
		position: absolute;
		bottom: 5rpx;
		right: 5rpx;
		width: 24rpx;
		height: 24rpx;
		background: #4caf50;
		border: 3rpx solid #fff;
		border-radius: 50%;
	}
	
	.user-info {
		flex: 1;
	}
	
	.username {
		font-size: 36rpx;
		font-weight: 600;
		color: #333;
		display: block;
		margin-bottom: 8rpx;
	}
	
	.user-status {
		font-size: 26rpx;
		color: #4caf50;
	}
	
	.edit-btn {
		background: linear-gradient(135deg, #ff6b9d, #c44569);
		color: #fff;
		border: none;
		border-radius: 40rpx;
		padding: 16rpx 32rpx;
		font-size: 26rpx;
		font-weight: 500;
	}
	
	/* 情侣关系卡片 */
	.relationship-card {
		background: rgba(255,255,255,0.95);
		border-radius: 24rpx;
		padding: 40rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 8rpx 24rpx rgba(255,154,158,0.2);
		backdrop-filter: blur(10rpx);
	}
	
	.relationship-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;
	}
	
	.card-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
	}
	
	.love-status {
		font-size: 24rpx;
		color: #ff6b9d;
		background: rgba(255,107,157,0.1);
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
	}
	
	.couple-info {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 40rpx;
	}
	
	.couple-avatar-section {
		text-align: center;
		flex: 1;
	}
	
	.couple-avatar {
		width: 80rpx;
		height: 80rpx;
		border-radius: 40rpx;
		border: 3rpx solid #ff6b9d;
		margin-bottom: 10rpx;
	}
	
	.couple-name {
		font-size: 24rpx;
		color: #666;
		display: block;
	}
	
	.love-connection {
		flex: 1;
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.love-line {
		width: 80rpx;
		height: 2rpx;
		background: #ff6b9d;
	}
	
	.love-heart {
		position: absolute;
		font-size: 24rpx;
		animation: pulse 2s infinite;
	}
	
	.love-stats {
		display: flex;
		justify-content: space-around;
		background: rgba(255,107,157,0.05);
		border-radius: 16rpx;
		padding: 30rpx;
	}
	
	.stat-item {
		text-align: center;
	}
	
	.stat-number {
		font-size: 32rpx;
		font-weight: 700;
		color: #ff6b9d;
		display: block;
		margin-bottom: 8rpx;
	}
	
	.stat-label {
		font-size: 24rpx;
		color: #999;
	}
	
	/* 功能菜单 */
	.menu-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 20rpx;
		margin-bottom: 40rpx;
	}
	
	.menu-item {
		background: rgba(255,255,255,0.95);
		border-radius: 20rpx;
		padding: 40rpx 20rpx;
		text-align: center;
		box-shadow: 0 6rpx 20rpx rgba(255,154,158,0.15);
		transition: all 0.3s ease;
	}
	
	.menu-item:active {
		transform: scale(0.95);
		box-shadow: 0 4rpx 16rpx rgba(255,154,158,0.25);
	}
	
	.menu-icon {
		font-size: 40rpx;
		margin-bottom: 16rpx;
		display: block;
	}
	
	.menu-text {
		font-size: 26rpx;
		color: #333;
		font-weight: 500;
	}
	
	/* 退出登录 */
	.logout-section {
		text-align: center;
	}
	
	.logout-btn {
		width: 100%;
		background: rgba(255,255,255,0.95);
		color: #ff6b9d;
		border: 2rpx solid #ff6b9d;
		border-radius: 50rpx;
		padding: 32rpx;
		font-size: 30rpx;
		font-weight: 600;
		box-shadow: 0 6rpx 20rpx rgba(255,154,158,0.15);
		transition: all 0.3s ease;
	}
	
	.logout-btn:active {
		background: rgba(255,107,157,0.1);
		transform: translateY(2rpx);
	}
	
	/* 动画 */
	@keyframes pulse {
		0%, 100% {
			transform: scale(1);
		}
		50% {
			transform: scale(1.1);
		}
	}
</style>
