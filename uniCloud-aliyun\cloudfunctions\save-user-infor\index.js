'use strict'
const https=require('https')
const db = uniCloud.database();
const userCollection = db.collection('users'); // 表名

exports.main = async (event, context) => {
	const {
		nickname,
		avatarUrl,
		gender,
		code
	} = event
	
	if(!code){
		return{
			code:0,
			msg:'缺少code参数'
		}
	}
	const appid='wx24140a6b09cd2e19'
	const secret='efb724cb45dc7754bc15add3a877ab31'
	const url=`https://api.weixin.qq.com/sns/jscode2session?appid=${appid}&secret=${secret}&js_code=${code}&grant_type=authorization_code`
	const openid= await new Promise((resolve,reject)=>{
		https.get(url,(res)=>{
			let data=''
			res.on('data',chunk=>{
				data+=chunk
			})
			res.on('end',()=>{
				try{
					const result=JSON.parse(data)
					resolve(result)
				}catch(err){
					reject(err)
				}
			}).on('error',(e)=>{
				reject(e)
			})
		})
	})
	
	const userInDB = await userCollection.where({
		openid
	}).get()
	if (userInDB.affectedDocs > 0) {
		return {
			code: 1,
			msg: '用户存在，登录成功',
			userInfo: userInDB.data[0]
		}
	} else {
			const currentTime = new Date();
		const res = await userCollection.add({
				openid,
				nickname: nickname || "微信用户",
				avatarUrl: avatarUrl,
				gender: gender === 1 ? "male" : (gender === 2 ? "female" : "unknown"),
				birthday: '',
				coupleId: '',
				register_date: currentTime,
				last_login_date: currentTime
		})
		return {
			code: 0,
			msg: "新用户以注册",
			userInfo: userInDB.data[0]

		}
	}

};