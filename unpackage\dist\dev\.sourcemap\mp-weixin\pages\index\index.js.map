{"version": 3, "file": "index.js", "sources": ["pages/index/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvaW5kZXgudnVl"], "sourcesContent": ["<template>\n\t<view class=\"container\">\n\t\t<!-- 顶部装饰 -->\n\t\t<view class=\"header-decoration\">\n\t\t\t<view class=\"floating-heart heart1\">💕</view>\n\t\t\t<view class=\"floating-heart heart2\">💖</view>\n\t\t\t<view class=\"floating-heart heart3\">💝</view>\n\t\t</view>\n\t\t\n\t\t<!-- 主标题区域 -->\n\t\t<view class=\"title-section\">\n\t\t\t<text class=\"main-title\">甜蜜时光</text>\n\t\t\t<text class=\"sub-title\">记录我们的每一个美好瞬间</text>\n\t\t</view>\n\t\t\n\t\t<!-- 功能卡片区域 -->\n\t\t<view class=\"feature-cards\">\n\t\t\t<view class=\"feature-card\" @tap=\"navigateTo('/pages/login/login')\">\n\t\t\t\t<view class=\"card-icon\">💑</view>\n\t\t\t\t<text class=\"card-title\">开始恋爱</text>\n\t\t\t\t<text class=\"card-desc\">记录甜蜜时光</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"feature-card\" @tap=\"navigateTo('/pages/user/user')\">\n\t\t\t\t<view class=\"card-icon\">👫</view>\n\t\t\t\t<text class=\"card-title\">我们的空间</text>\n\t\t\t\t<text class=\"card-desc\">查看情侣档案</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"feature-card\">\n\t\t\t\t<view class=\"card-icon\">📷</view>\n\t\t\t\t<text class=\"card-title\">甜蜜相册</text>\n\t\t\t\t<text class=\"card-desc\">珍藏美好回忆</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"feature-card\">\n\t\t\t\t<view class=\"card-icon\">💌</view>\n\t\t\t\t<text class=\"card-title\">情话绵绵</text>\n\t\t\t\t<text class=\"card-desc\">写给彼此的话</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"feature-card\">\n\t\t\t\t<view class=\"card-icon\">🎂</view>\n\t\t\t\t<text class=\"card-title\">纪念日</text>\n\t\t\t\t<text class=\"card-desc\">重要日子提醒</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"feature-card\">\n\t\t\t\t<view class=\"card-icon\">🎁</view>\n\t\t\t\t<text class=\"card-title\">愿望清单</text>\n\t\t\t\t<text class=\"card-desc\">想要的小礼物</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tnavigateTo(url) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: url\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style scoped>\n\t.container {\n\t\tmin-height: 100vh;\n\t\tbackground: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);\n\t\tpadding: 40rpx 30rpx;\n\t\tposition: relative;\n\t}\n\t\n\t/* 顶部装饰 */\n\t.header-decoration {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\theight: 200rpx;\n\t\tpointer-events: none;\n\t}\n\t\n\t.floating-heart {\n\t\tposition: absolute;\n\t\tfont-size: 40rpx;\n\t\topacity: 0.6;\n\t\tanimation: float 4s ease-in-out infinite;\n\t}\n\t\n\t.heart1 {\n\t\ttop: 60rpx;\n\t\tleft: 10%;\n\t\tanimation-delay: 0s;\n\t}\n\t\n\t.heart2 {\n\t\ttop: 100rpx;\n\t\tright: 15%;\n\t\tanimation-delay: 1.5s;\n\t}\n\t\n\t.heart3 {\n\t\ttop: 140rpx;\n\t\tleft: 50%;\n\t\tanimation-delay: 3s;\n\t}\n\t\n\t/* 主标题 */\n\t.title-section {\n\t\ttext-align: center;\n\t\tmargin: 120rpx 0 80rpx;\n\t}\n\t\n\t.main-title {\n\t\tfont-size: 56rpx;\n\t\tfont-weight: 700;\n\t\tcolor: #fff;\n\t\ttext-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);\n\t\tdisplay: block;\n\t\tmargin-bottom: 20rpx;\n\t}\n\t\n\t.sub-title {\n\t\tfont-size: 28rpx;\n\t\tcolor: rgba(255,255,255,0.9);\n\t\tfont-weight: 400;\n\t}\n\t\n\t/* 功能卡片 */\n\t.feature-cards {\n\t\tdisplay: grid;\n\t\tgrid-template-columns: 1fr 1fr;\n\t\tgap: 30rpx;\n\t\tmargin-bottom: 80rpx;\n\t}\n\t\n\t.feature-card {\n\t\tbackground: rgba(255,255,255,0.95);\n\t\tborder-radius: 24rpx;\n\t\tpadding: 40rpx 30rpx;\n\t\ttext-align: center;\n\t\tbox-shadow: 0 8rpx 24rpx rgba(255,154,158,0.2);\n\t\ttransition: all 0.3s ease;\n\t\tbackdrop-filter: blur(10rpx);\n\t}\n\t\n\t.feature-card:active {\n\t\ttransform: scale(0.95);\n\t\tbox-shadow: 0 4rpx 12rpx rgba(255,154,158,0.3);\n\t}\n\t\n\t.card-icon {\n\t\tfont-size: 48rpx;\n\t\tmargin-bottom: 20rpx;\n\t\tdisplay: block;\n\t}\n\t\n\t.card-title {\n\t\tfont-size: 30rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #333;\n\t\tdisplay: block;\n\t\tmargin-bottom: 10rpx;\n\t}\n\t\n\t.card-desc {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t\tline-height: 1.4;\n\t}\n\t\n\t/* 登录提示 */\n\t.login-prompt {\n\t\ttext-align: center;\n\t\tbackground: rgba(255,255,255,0.9);\n\t\tborder-radius: 24rpx;\n\t\tpadding: 40rpx;\n\t\tbox-shadow: 0 8rpx 24rpx rgba(255,154,158,0.2);\n\t}\n\t\n\t.prompt-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t\tmargin-bottom: 30rpx;\n\t\tdisplay: block;\n\t}\n\t\n\t.login-btn {\n\t\tbackground: linear-gradient(135deg, #ff6b9d, #c44569);\n\t\tcolor: #fff;\n\t\tborder: none;\n\t\tborder-radius: 50rpx;\n\t\tpadding: 24rpx 60rpx;\n\t\tfont-size: 30rpx;\n\t\tfont-weight: 600;\n\t\tbox-shadow: 0 6rpx 20rpx rgba(196,69,105,0.3);\n\t\ttransition: all 0.3s ease;\n\t}\n\t\n\t.login-btn:active {\n\t\ttransform: translateY(2rpx);\n\t\tbox-shadow: 0 4rpx 16rpx rgba(196,69,105,0.4);\n\t}\n\t\n\t/* 动画 */\n\t@keyframes float {\n\t\t0%, 100% {\n\t\t\ttransform: translateY(0px);\n\t\t}\n\t\t50% {\n\t\t\ttransform: translateY(-20rpx);\n\t\t}\n\t}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/auglar/text/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA0DC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO,CAEP;AAAA,EACA;AAAA,EACD,SAAS;AAAA,IACR,WAAW,KAAK;AACfA,oBAAAA,MAAI,WAAW;AAAA,QACd;AAAA,MACD,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;ACtED,GAAG,WAAW,eAAe;"}