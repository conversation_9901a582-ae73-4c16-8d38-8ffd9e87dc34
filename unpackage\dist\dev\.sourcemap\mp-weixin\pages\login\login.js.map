{"version": 3, "file": "login.js", "sources": ["pages/login/login.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbG9naW4vbG9naW4udnVl"], "sourcesContent": ["<template>\n\t<view class=\"login-container\">\n\t\t<!-- 顶部装饰 -->\n\t\t<view class=\"top-decoration\">\n\t\t\t<view class=\"love-icon\">💕</view>\n\t\t</view>\n\t\t\n\t\t<!-- 登录卡片 -->\n\t\t<view class=\"login-card\">\n\t\t\t<view class=\"logo-section\">\n\t\t\t\t<view class=\"app-logo\">💑</view>\n\t\t\t\t<text class=\"app-name\">甜蜜时光</text>\n\t\t\t\t<text class=\"app-slogan\">记录我们的爱情故事</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 微信登录按钮 -->\n\t\t\t<button class=\"wechat-login-btn\" @click=\"handleWechatLogin\">\n\t\t\t\t<view class=\"btn-content\">\n\t\t\t\t\t<text class=\"wechat-icon\">💚</text>\n\t\t\t\t\t<text class=\"btn-text\">微信一键登录</text>\n\t\t\t\t</view>\n\t\t\t</button>\n\t\t\t\n\t\t<!-- 用户协议 -->\n\t\t<view class=\"agreement-section\">\n\t\t\t<view class=\"agreement-check\">\n\t\t\t\t<checkbox-group @change=\"onAgreementChange\">\n\t\t\t\t\t<checkbox value=\"agree\" :checked=\"isAgreed\" color=\"#ff6b9d\"/>\n\t\t\t\t</checkbox-group>\n\t\t\t\t<text class=\"agreement-text\">\n\t\t\t\t\t我已阅读并同意\n\t\t\t\t\t<text class=\"link-text\">《用户协议》</text>\n\t\t\t\t\t和\n\t\t\t\t\t<text class=\"link-text\">《隐私政策》</text>\n\t\t\t\t</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 底部装饰 -->\n\t\t<view class=\"bottom-decoration\">\n\t\t\t<view class=\"heart-animation\">\n\t\t\t\t<text class=\"heart\">💖</text>\n\t\t\t\t<text class=\"heart\">💕</text>\n\t\t\t\t<text class=\"heart\">💝</text>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tverifyCode: '',\n\t\t\t\tisAgreed: false,\n\t\t\t\tcountdown: 0,\n\t\t\t\tuserInfo: null,\n\t\t\t\tisLoading: false\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t// 微信登录\n\t\tasync\thandleWechatLogin() {\n\t\t\t//判断勾选协议\n\t\t\tconsole.log(\"登录点击拉\")\n\t\t\t\tif (!this.isAgreed) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请先同意用户协议',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\r\n\t\t\t\tif(!uni.getStorageInfoSync(\"userInfo\")){\r\n\t\t\t\t\t//用户登录功能\r\n\t\t\t\t\ttry{\r\n\t\t\t\t\t\t uni.showLoading({title: '登录中...'});\r\n\t\t\t\t\t\tconst userProfileRes=await new Promise((resolve,reject)=>{\r\n\t\t\t\t\t\t\tuni.getUserProfile({\r\n\t\t\t\t\t\t\t\tdesc:\"登录后可以同步数据\",\r\n\t\t\t\t\t\t\t\tsuccess:resolve,\r\n\t\t\t\t\t\t\t\tfail:reject\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t},1000)\r\n\t\t\t\t\t\tconst userInfo=userProfileRes.userInfo\r\n\t\t\t\t\t\tthis.userInfo=userInfo\r\n\t\t\t\t\t\t//获取临时code‘\r\n\t\t\t\t\t\tconst loginRes=await new Promise((resolve,reject)=>{\r\n\t\t\t\t\t\t\tuni.login({\r\n\t\t\t\t\t\t\t\tprovider:'weixin',\r\n\t\t\t\t\t\t\t\tsuccess:resolve,\r\n\t\t\t\t\t\t\t\tfail:reject\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t},1000)\r\n\t\t\t\t\t\tconst code =loginRes.code;\r\n\t\t\t\t\t\tif(!code){\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle:\"获取code失败\",\r\n\t\t\t\t\t\t\t\ticon:\"none\"\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\treturn\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tconst saveRes=await uniCloud.callFunction({\r\n\t\t\t\t\t\t\tname:\"save-user-infor\",\r\n\t\t\t\t\t\t\tdata:{\r\n\t\t\t\t\t\t\t\t'nickname':userInfo.nickname,\r\n\t\t\t\t\t\t\t\t'avatarUrl':userInfo.avatarUrl,\r\n\t\t\t\t\t\t\t\t'gender':userInfo.gender,\r\n\t\t\t\t\t\t\t\t'code':code,\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},1000)\r\n\t\t\t\t\t\tuni.setStorageSync('userInfo',this.userInfo)\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle:'登录成功',\r\n\t\t\t\t\t\t\ticon:\"success\"\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\turl:\"/pages/index/index\"\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tconsole.log(userInfo)\r\n\t\t\t\t\t}catch(err){\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle:\"登录失败\",\r\n\t\t\t\t\t\t\ticon:\"none\"\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}else{\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle:'登录成功',\r\n\t\t\t\t\t\ticon:\"success\"\r\n\t\t\t\t\t})\r\n\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\turl:\"/pages/index/index\"\r\n\t\t\t\t\t})\r\n\t\t\t\t}\n\n},\n//勾选用户协议\nonAgreementChange(){\n\tthis.isAgreed=true\n}\n}}\n</script>\n\n<style scoped>\n\t.login-container {\n\t\tmin-height: 100vh;\n\t\tbackground: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);\n\t\tpadding: 40rpx;\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\t\n\t/* 顶部装饰 */\n\t.top-decoration {\n\t\ttext-align: center;\n\t\tmargin-bottom: 60rpx;\n\t}\n\t\n\t.love-icon {\n\t\tfont-size: 80rpx;\n\t\tanimation: pulse 2s infinite;\n\t}\n\t\n\t/* 登录卡片 */\n\t.login-card {\n\t\tbackground: rgba(255,255,255,0.95);\n\t\tborder-radius: 32rpx;\n\t\tpadding: 60rpx 40rpx;\n\t\tbox-shadow: 0 12rpx 40rpx rgba(255,154,158,0.25);\n\t\tbackdrop-filter: blur(10rpx);\n\t}\n\t\n\t/* Logo区域 */\n\t.logo-section {\n\t\ttext-align: center;\n\t\tmargin-bottom: 60rpx;\n\t}\n\t\n\t.app-logo {\n\t\tfont-size: 100rpx;\n\t\tmargin-bottom: 20rpx;\n\t\tdisplay: block;\n\t}\n\t\n\t.app-name {\n\t\tfont-size: 48rpx;\n\t\tfont-weight: 700;\n\t\tcolor: #333;\n\t\tdisplay: block;\n\t\tmargin-bottom: 10rpx;\n\t}\n\t\n\t.app-slogan {\n\t\tfont-size: 28rpx;\n\t\tcolor: #999;\n\t}\n\t\n\t/* 微信登录按钮 */\n\t.wechat-login-btn {\n\t\twidth: 100%;\n\t\tbackground: #07c160;\n\t\tborder: none;\n\t\tborder-radius: 50rpx;\n\t\tpadding: 32rpx;\n\t\tmargin-bottom: 40rpx;\n\t\tbox-shadow: 0 8rpx 24rpx rgba(7,193,96,0.3);\n\t\ttransition: all 0.3s ease;\n\t}\n\t\n\t.wechat-login-btn:active {\n\t\ttransform: translateY(2rpx);\n\t\tbox-shadow: 0 6rpx 20rpx rgba(7,193,96,0.4);\n\t}\n\t\n\t.btn-content {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\t\n\t.wechat-icon {\n\t\tfont-size: 36rpx;\n\t\tmargin-right: 16rpx;\n\t}\n\t\n\t.btn-text {\n\t\tfont-size: 32rpx;\n\t\tcolor: #fff;\n\t\tfont-weight: 600;\n\t}\n\t\n\t/* 手机号登录 */\n\t.phone-login-section {\n\t\tborder-top: 1rpx solid #f0f0f0;\n\t\tpadding-top: 40rpx;\n\t}\n\t\n\t.section-title {\n\t\ttext-align: center;\n\t\tfont-size: 28rpx;\n\t\tcolor: #999;\n\t\tmargin-bottom: 40rpx;\n\t\tdisplay: block;\n\t}\n\t\n\t.input-group {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: 30rpx;\n\t\tbackground: #f8f8f8;\n\t\tborder-radius: 50rpx;\n\t\tpadding: 0 30rpx;\n\t\theight: 100rpx;\n\t}\n\t\n\t.phone-input, .code-input {\n\t\tflex: 1;\n\t\tfont-size: 30rpx;\n\t\tcolor: #333;\n\t\tbackground: transparent;\n\t\tborder: none;\n\t}\n\t\n\t.get-code-btn {\n\t\tbackground: linear-gradient(135deg, #ff6b9d, #c44569);\n\t\tcolor: #fff;\n\t\tborder: none;\n\t\tborder-radius: 40rpx;\n\t\tpadding: 16rpx 32rpx;\n\t\tfont-size: 26rpx;\n\t\tfont-weight: 500;\n\t}\n\t\n\t.phone-login-btn {\n\t\twidth: 100%;\n\t\tbackground: linear-gradient(135deg, #ff6b9d, #c44569);\n\t\tcolor: #fff;\n\t\tborder: none;\n\t\tborder-radius: 50rpx;\n\t\tpadding: 32rpx;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 600;\n\t\tbox-shadow: 0 8rpx 24rpx rgba(196,69,105,0.3);\n\t\ttransition: all 0.3s ease;\n\t}\n\t\n\t.phone-login-btn:active {\n\t\ttransform: translateY(2rpx);\n\t\tbox-shadow: 0 6rpx 20rpx rgba(196,69,105,0.4);\n\t}\n\t\n\t/* 用户协议 */\n\t.agreement-section {\n\t\tmargin-top: 40rpx;\n\t\ttext-align: center;\n\t}\n\t\n\t.agreement-check {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\t\n\t.agreement-text {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t\tmargin-left: 16rpx;\n\t}\n\t\n\t.link-text {\n\t\tcolor: #ff6b9d;\n\t}\n\t\n\t/* 底部装饰 */\n\t.bottom-decoration {\n\t\tposition: fixed;\n\t\tbottom: 60rpx;\n\t\tleft: 50%;\n\t\ttransform: translateX(-50%);\n\t}\n\t\n\t.heart-animation {\n\t\tdisplay: flex;\n\t\tgap: 20rpx;\n\t}\n\t\n\t.heart {\n\t\tfont-size: 32rpx;\n\t\topacity: 0.6;\n\t\tanimation: bounce 2s infinite;\n\t}\n\t\n\t.heart:nth-child(2) {\n\t\tanimation-delay: 0.5s;\n\t}\n\t\n\t.heart:nth-child(3) {\n\t\tanimation-delay: 1s;\n\t}\n\t\n\t/* 动画 */\n\t@keyframes pulse {\n\t\t0%, 100% {\n\t\t\ttransform: scale(1);\n\t\t}\n\t\t50% {\n\t\t\ttransform: scale(1.1);\n\t\t}\n\t}\n\t\n\t@keyframes bounce {\n\t\t0%, 100% {\n\t\t\ttransform: translateY(0);\n\t\t}\n\t\t50% {\n\t\t\ttransform: translateY(-10rpx);\n\t\t}\n\t}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/auglar/text/pages/login/login.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "uniCloud"], "mappings": ";;AAmDC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,MACV,WAAW;AAAA,IACZ;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA,IAET,MAAM,oBAAoB;AAEzBA,oBAAAA,MAAA,MAAA,OAAA,+BAAY,OAAO;AAClB,UAAI,CAAC,KAAK,UAAU;AACnBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AACA,UAAG,CAACA,cAAG,MAAC,mBAAmB,UAAU,GAAE;AAEtC,YAAG;AACDA,wBAAAA,MAAI,YAAY,EAAC,OAAO,SAAQ,CAAC;AAClC,gBAAM,iBAAe,MAAM,IAAI,QAAQ,CAAC,SAAQ,WAAS;AACxDA,0BAAAA,MAAI,eAAe;AAAA,cAClB,MAAK;AAAA,cACL,SAAQ;AAAA,cACR,MAAK;AAAA,aACL;AAAA,UACD,GAAC,GAAI;AACN,gBAAM,WAAS,eAAe;AAC9B,eAAK,WAAS;AAEd,gBAAM,WAAS,MAAM,IAAI,QAAQ,CAAC,SAAQ,WAAS;AAClDA,0BAAAA,MAAI,MAAM;AAAA,cACT,UAAS;AAAA,cACT,SAAQ;AAAA,cACR,MAAK;AAAA,aACL;AAAA,UACD,GAAC,GAAI;AACN,gBAAM,OAAM,SAAS;AACrB,cAAG,CAAC,MAAK;AACRA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAM;AAAA,cACN,MAAK;AAAA,aACL;AACD;AAAA,UACD;AACA,gBAAM,UAAQ,MAAMC,cAAQ,GAAC,aAAa;AAAA,YACzC,MAAK;AAAA,YACL,MAAK;AAAA,cACJ,YAAW,SAAS;AAAA,cACpB,aAAY,SAAS;AAAA,cACrB,UAAS,SAAS;AAAA,cAClB,QAAO;AAAA,YACP;AAAA,UACD,GAAC,GAAI;AACND,wBAAAA,MAAI,eAAe,YAAW,KAAK,QAAQ;AAC3CA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAM;AAAA,YACN,MAAK;AAAA,WACL;AACDA,wBAAAA,MAAI,UAAU;AAAA,YACb,KAAI;AAAA,WACJ;AACDA,wBAAAA,MAAY,MAAA,OAAA,gCAAA,QAAQ;AAAA,QACpB,SAAM,KAAI;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAM;AAAA,YACN,MAAK;AAAA,WACL;AAAA,QACF;AAAA,aACI;AACJA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAM;AAAA,UACN,MAAK;AAAA,SACL;AACDA,sBAAAA,MAAI,UAAU;AAAA,UACb,KAAI;AAAA,SACJ;AAAA,MACF;AAAA,IAEJ;AAAA;AAAA,IAEA,oBAAmB;AAClB,WAAK,WAAS;AAAA,IACf;AAAA,EACA;AAAC;;;;;;;;;AC5ID,GAAG,WAAW,eAAe;"}