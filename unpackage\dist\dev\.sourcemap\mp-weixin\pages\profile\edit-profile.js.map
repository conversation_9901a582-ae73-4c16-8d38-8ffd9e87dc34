{"version": 3, "file": "edit-profile.js", "sources": ["pages/profile/edit-profile.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHJvZmlsZS9lZGl0LXByb2ZpbGUudnVl"], "sourcesContent": ["<template>\n\t<view class=\"edit-profile-container\">\n\t\t<!-- 页面标题 -->\n\t\t<view class=\"page-header\">\n\t\t\t<text class=\"page-title\">编辑资料</text>\n\t\t</view>\n\t\t\n\t\t<!-- 头像编辑区域 -->\n\t\t<view class=\"avatar-section\">\n\t\t\t<view class=\"avatar-container\">\n\t\t\t\t<image class=\"profile-avatar\" src=\"/static/logo.png\" mode=\"aspectFill\"></image>\n\t\t\t\t<view class=\"avatar-edit-overlay\">\n\t\t\t\t\t<view class=\"camera-icon\">📷</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<text class=\"avatar-tip\">点击更换头像</text>\n\t\t</view>\n\t\t\n\t\t<!-- 基本信息表单 -->\n\t\t<view class=\"form-section\">\n\t\t\t<text class=\"section-title\">基本信息</text>\n\t\t\t<view class=\"form-list\">\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"form-label\">昵称</text>\n\t\t\t\t\t<input \n\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\tplaceholder=\"请输入昵称\" \n\t\t\t\t\t\tvalue=\"甜蜜情侣\"\n\t\t\t\t\t\tmaxlength=\"20\"\n\t\t\t\t\t/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"form-label\">性别</text>\n\t\t\t\t\t<view class=\"gender-selector\">\n\t\t\t\t\t\t<view class=\"gender-option active\">\n\t\t\t\t\t\t\t<text class=\"gender-text\">女生</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"gender-option\">\n\t\t\t\t\t\t\t<text class=\"gender-text\">男生</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"form-label\">生日</text>\n\t\t\t\t\t<picker mode=\"date\" value=\"1995-01-01\">\n\t\t\t\t\t\t<view class=\"picker-wrapper\">\n\t\t\t\t\t\t\t<text class=\"picker-text\">1995-01-01</text>\n\t\t\t\t\t\t\t<text class=\"picker-arrow\">></text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</picker>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"form-label\">地区</text>\n\t\t\t\t\t<picker mode=\"region\" value=\"['广东省', '深圳市', '南山区']\">\n\t\t\t\t\t\t<view class=\"picker-wrapper\">\n\t\t\t\t\t\t\t<text class=\"picker-text\">广东 深圳 南山</text>\n\t\t\t\t\t\t\t<text class=\"picker-arrow\">></text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</picker>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 个性签名 -->\n\t\t<view class=\"form-section\">\n\t\t\t<text class=\"section-title\">个性签名</text>\n\t\t\t<view class=\"signature-wrapper\">\n\t\t\t\t<textarea \n\t\t\t\t\tclass=\"signature-textarea\" \n\t\t\t\t\tplaceholder=\"写下你的个性签名吧~\" \n\t\t\t\t\tmaxlength=\"50\"\n\t\t\t\t\tvalue=\"愿得一人心，白首不分离 💕\"\n\t\t\t\t></textarea>\n\t\t\t\t<text class=\"char-counter\">25/50</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 情侣信息 -->\n\t\t<view class=\"form-section\">\n\t\t\t<text class=\"section-title\">情侣信息</text>\n\t\t\t<view class=\"form-list\">\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"form-label\">恋爱纪念日</text>\n\t\t\t\t\t<picker mode=\"date\" value=\"2023-02-14\">\n\t\t\t\t\t\t<view class=\"picker-wrapper\">\n\t\t\t\t\t\t\t<text class=\"picker-text\">2023-02-14</text>\n\t\t\t\t\t\t\t<text class=\"picker-arrow\">></text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</picker>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"form-label\">对TA的称呼</text>\n\t\t\t\t\t<input \n\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\tplaceholder=\"给TA起个昵称\" \n\t\t\t\t\t\tvalue=\"小宝贝\"\n\t\t\t\t\t\tmaxlength=\"10\"\n\t\t\t\t\t/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"form-label\">关系状态</text>\n\t\t\t\t\t<picker range=\"['热恋中', '稳定期', '蜜月期', '磨合期']\" value=\"0\">\n\t\t\t\t\t\t<view class=\"picker-wrapper\">\n\t\t\t\t\t\t\t<text class=\"picker-text\">热恋中</text>\n\t\t\t\t\t\t\t<text class=\"picker-arrow\">></text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</picker>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 保存按钮 -->\n\t\t<view class=\"save-section\">\n\t\t\t<button class=\"save-btn\" @tap=\"handleSave\">\n\t\t\t\t保存修改\n\t\t\t</button>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\thandleSave() {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '保存成功',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t});\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.navigateBack();\n\t\t\t\t}, 1500);\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style scoped>\n\t.edit-profile-container {\n\t\tmin-height: 100vh;\n\t\tbackground: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);\n\t\tpadding: 40rpx 30rpx;\n\t}\n\t\n\t/* 页面标题 */\n\t.page-header {\n\t\ttext-align: center;\n\t\tmargin-bottom: 40rpx;\n\t}\n\t\n\t.page-title {\n\t\tfont-size: 40rpx;\n\t\tfont-weight: 700;\n\t\tcolor: #fff;\n\t\ttext-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);\n\t}\n\t\n\t/* 头像编辑 */\n\t.avatar-section {\n\t\ttext-align: center;\n\t\tbackground: rgba(255,255,255,0.95);\n\t\tborder-radius: 24rpx;\n\t\tpadding: 60rpx 40rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tbox-shadow: 0 8rpx 24rpx rgba(255,154,158,0.2);\n\t\tbackdrop-filter: blur(10rpx);\n\t}\n\t\n\t.avatar-container {\n\t\tposition: relative;\n\t\tdisplay: inline-block;\n\t\tmargin-bottom: 20rpx;\n\t}\n\t\n\t.profile-avatar {\n\t\twidth: 160rpx;\n\t\theight: 160rpx;\n\t\tborder-radius: 80rpx;\n\t\tborder: 4rpx solid #ff6b9d;\n\t}\n\t\n\t.avatar-edit-overlay {\n\t\tposition: absolute;\n\t\tbottom: -8rpx;\n\t\tright: -8rpx;\n\t\twidth: 48rpx;\n\t\theight: 48rpx;\n\t\tbackground: #ff6b9d;\n\t\tborder-radius: 24rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(255,107,157,0.3);\n\t}\n\t\n\t.camera-icon {\n\t\tfont-size: 20rpx;\n\t\tcolor: #fff;\n\t}\n\t\n\t.avatar-tip {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t}\n\t\n\t/* 表单区域 */\n\t.form-section {\n\t\tmargin-bottom: 30rpx;\n\t}\n\t\n\t.section-title {\n\t\tfont-size: 28rpx;\n\t\tfont-weight: 600;\n\t\tcolor: rgba(255,255,255,0.9);\n\t\tmargin-bottom: 20rpx;\n\t\tmargin-left: 20rpx;\n\t\tdisplay: block;\n\t}\n\t\n\t.form-list {\n\t\tbackground: rgba(255,255,255,0.95);\n\t\tborder-radius: 24rpx;\n\t\toverflow: hidden;\n\t\tbox-shadow: 0 8rpx 24rpx rgba(255,154,158,0.2);\n\t\tbackdrop-filter: blur(10rpx);\n\t}\n\t\n\t.form-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 40rpx;\n\t\tborder-bottom: 1rpx solid rgba(0,0,0,0.05);\n\t}\n\t\n\t.form-item:last-child {\n\t\tborder-bottom: none;\n\t}\n\t\n\t.form-label {\n\t\twidth: 160rpx;\n\t\tfont-size: 30rpx;\n\t\tcolor: #333;\n\t\tfont-weight: 500;\n\t}\n\t\n\t.form-input {\n\t\tflex: 1;\n\t\tfont-size: 30rpx;\n\t\tcolor: #333;\n\t\ttext-align: right;\n\t\tbackground: transparent;\n\t\tborder: none;\n\t}\n\t\n\t.form-input::placeholder {\n\t\tcolor: #ccc;\n\t}\n\t\n\t/* 性别选择器 */\n\t.gender-selector {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tjustify-content: flex-end;\n\t\tgap: 16rpx;\n\t}\n\t\n\t.gender-option {\n\t\tpadding: 16rpx 28rpx;\n\t\tborder-radius: 40rpx;\n\t\tborder: 2rpx solid rgba(255,107,157,0.3);\n\t\ttransition: all 0.3s ease;\n\t}\n\t\n\t.gender-option.active {\n\t\tbackground: #ff6b9d;\n\t\tborder-color: #ff6b9d;\n\t}\n\t\n\t.gender-text {\n\t\tfont-size: 26rpx;\n\t\tcolor: #666;\n\t}\n\t\n\t.gender-option.active .gender-text {\n\t\tcolor: #fff;\n\t}\n\t\n\t/* 选择器 */\n\t.picker-wrapper {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: flex-end;\n\t}\n\t\n\t.picker-text {\n\t\tfont-size: 30rpx;\n\t\tcolor: #333;\n\t\tmargin-right: 12rpx;\n\t}\n\t\n\t.picker-arrow {\n\t\tfont-size: 24rpx;\n\t\tcolor: #ccc;\n\t}\n\t\n\t/* 个性签名 */\n\t.signature-wrapper {\n\t\tbackground: rgba(255,255,255,0.95);\n\t\tborder-radius: 24rpx;\n\t\tpadding: 40rpx;\n\t\tbox-shadow: 0 8rpx 24rpx rgba(255,154,158,0.2);\n\t\tbackdrop-filter: blur(10rpx);\n\t}\n\t\n\t.signature-textarea {\n\t\twidth: 100%;\n\t\tmin-height: 120rpx;\n\t\tfont-size: 30rpx;\n\t\tcolor: #333;\n\t\tline-height: 1.6;\n\t\tbackground: rgba(255,107,157,0.05);\n\t\tborder-radius: 16rpx;\n\t\tpadding: 24rpx;\n\t\tborder: 1rpx solid rgba(255,107,157,0.1);\n\t\tmargin-bottom: 16rpx;\n\t}\n\t\n\t.signature-textarea::placeholder {\n\t\tcolor: #ccc;\n\t}\n\t\n\t.char-counter {\n\t\ttext-align: right;\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t\tdisplay: block;\n\t}\n\t\n\t/* 保存按钮 */\n\t.save-section {\n\t\tmargin-top: 60rpx;\n\t\ttext-align: center;\n\t}\n\t\n\t.save-btn {\n\t\twidth: 100%;\n\t\tbackground: linear-gradient(135deg, #ff6b9d, #c44569);\n\t\tcolor: #fff;\n\t\tborder: none;\n\t\tborder-radius: 50rpx;\n\t\tpadding: 36rpx;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 600;\n\t\tbox-shadow: 0 8rpx 24rpx rgba(196,69,105,0.3);\n\t\ttransition: all 0.3s ease;\n\t}\n\t\n\t.save-btn:active {\n\t\ttransform: translateY(2rpx);\n\t\tbox-shadow: 0 6rpx 20rpx rgba(196,69,105,0.4);\n\t}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/auglar/text/pages/profile/edit-profile.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAyHC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO,CAEP;AAAA,EACA;AAAA,EACD,SAAS;AAAA,IACR,aAAa;AACZA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AACD,iBAAW,MAAM;AAChBA,sBAAG,MAAC,aAAY;AAAA,MAChB,GAAE,IAAI;AAAA,IACR;AAAA,EACD;AACD;;;;;;;;ACzID,GAAG,WAAW,eAAe;"}