{"version": 3, "file": "edit-profile.js", "sources": ["pages/profile/edit-profile.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHJvZmlsZS9lZGl0LXByb2ZpbGUudnVl"], "sourcesContent": ["<template>\n\t<view class=\"container\">\n\t\t<!-- 背景装饰 -->\n\t\t<view class=\"bg-decoration\">\n\t\t\t<view class=\"decoration-circle circle-1\"></view>\n\t\t\t<view class=\"decoration-circle circle-2\"></view>\n\t\t\t<view class=\"decoration-circle circle-3\"></view>\n\t\t</view>\n\n\t\t<!-- 头像编辑区域 -->\n\t\t<view class=\"avatar-section\">\n\t\t\t<view class=\"avatar-card\">\n\t\t\t\t<view class=\"avatar-container\" @tap=\"changeAvatar\">\n\t\t\t\t\t<image :src=\"userProfile.avatarUrl || defaultAvatar\" class=\"profile-avatar\" />\n\t\t\t\t\t<view class=\"avatar-border\"></view>\n\t\t\t\t\t<view class=\"camera-icon\">📷</view>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"avatar-tip\">点击更换头像</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 基本信息编辑 -->\n\t\t<view class=\"form-section\">\n\t\t\t<view class=\"section-header\">\n\t\t\t\t<text class=\"section-title\">基本信息</text>\n\t\t\t\t<text class=\"section-icon\">👤</text>\n\t\t\t</view>\n\t\t\t<view class=\"form-card\">\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<view class=\"form-label\">\n\t\t\t\t\t\t<text class=\"label-icon\">✏️</text>\n\t\t\t\t\t\t<text class=\"label-text\">昵称</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<input \n\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\tv-model=\"userProfile.nickName\" \n\t\t\t\t\t\tplaceholder=\"请输入昵称\"\n\t\t\t\t\t\tmaxlength=\"20\"\n\t\t\t\t\t/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<view class=\"form-label\">\n\t\t\t\t\t\t<text class=\"label-icon\">💭</text>\n\t\t\t\t\t\t<text class=\"label-text\">个性签名</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<textarea \n\t\t\t\t\t\tclass=\"form-textarea\" \n\t\t\t\t\t\tv-model=\"userProfile.signature\" \n\t\t\t\t\t\tplaceholder=\"写下你的个性签名...\"\n\t\t\t\t\t\tmaxlength=\"50\"\n\t\t\t\t\t/>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 详细信息编辑 -->\n\t\t<view class=\"form-section\">\n\t\t\t<view class=\"section-header\">\n\t\t\t\t<text class=\"section-title\">详细信息</text>\n\t\t\t\t<text class=\"section-icon\">📋</text>\n\t\t\t</view>\n\t\t\t<view class=\"form-card\">\n\t\t\t\t<view class=\"form-item\" @tap=\"selectBirthday\">\n\t\t\t\t\t<view class=\"form-label\">\n\t\t\t\t\t\t<text class=\"label-icon\">🎂</text>\n\t\t\t\t\t\t<text class=\"label-text\">生日</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"form-value\">\n\t\t\t\t\t\t<text class=\"value-text\">{{ userProfile.birthday || '请选择生日' }}</text>\n\t\t\t\t\t\t<text class=\"form-arrow\">→</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"form-item\" @tap=\"selectGender\">\n\t\t\t\t\t<view class=\"form-label\">\n\t\t\t\t\t\t<text class=\"label-icon\">⚧️</text>\n\t\t\t\t\t\t<text class=\"label-text\">性别</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"form-value\">\n\t\t\t\t\t\t<text class=\"value-text\">{{ genderText }}</text>\n\t\t\t\t\t\t<text class=\"form-arrow\">→</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"form-item\" @tap=\"selectRegion\">\n\t\t\t\t\t<view class=\"form-label\">\n\t\t\t\t\t\t<text class=\"label-icon\">📍</text>\n\t\t\t\t\t\t<text class=\"label-text\">地区</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"form-value\">\n\t\t\t\t\t\t<text class=\"value-text\">{{ userProfile.region || '请选择地区' }}</text>\n\t\t\t\t\t\t<text class=\"form-arrow\">→</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 操作按钮 -->\n\t\t<view class=\"action-section\">\n\t\t\t<button class=\"save-btn\" @tap=\"saveProfile\">\n\t\t\t\t<text class=\"save-text\">保存修改</text>\n\t\t\t</button>\n\t\t\t<button class=\"cancel-btn\" @tap=\"cancelEdit\">\n\t\t\t\t<text class=\"cancel-text\">取消</text>\n\t\t\t</button>\n\t\t</view>\n\n\t\t<!-- 日期选择器 -->\n\t\t<picker \n\t\t\tv-if=\"showDatePicker\"\n\t\t\tmode=\"date\" \n\t\t\t:value=\"userProfile.birthday\"\n\t\t\t@change=\"onDateChange\"\n\t\t\t@cancel=\"showDatePicker = false\"\n\t\t>\n\t\t\t<view></view>\n\t\t</picker>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tuserProfile: {\n\t\t\t\tavatarUrl: '',\n\t\t\t\tnickName: '',\n\t\t\t\tsignature: '',\n\t\t\t\tbirthday: '',\n\t\t\t\tgender: 0, // 0:未知 1:男 2:女\n\t\t\t\tregion: ''\n\t\t\t},\n\t\t\tdefaultAvatar: '/static/default-avatar.png',\n\t\t\tshowDatePicker: false,\n\t\t\toriginalProfile: {} // 保存原始数据用于取消操作\n\t\t};\n\t},\n\tcomputed: {\n\t\tgenderText() {\n\t\t\tconst genderMap = { 0: '未设置', 1: '男', 2: '女' };\n\t\t\treturn genderMap[this.userProfile.gender] || '未设置';\n\t\t}\n\t},\n\tonLoad() {\n\t\tthis.loadUserProfile();\n\t},\n\tmethods: {\n\t\t// 加载用户资料\n\t\tloadUserProfile() {\n\t\t\tconst userInfo = uni.getStorageSync('userInfo');\n\t\t\tif (userInfo) {\n\t\t\t\tthis.userProfile = {\n\t\t\t\t\tavatarUrl: userInfo.avatarUrl || '',\n\t\t\t\t\tnickName: userInfo.nickName || '',\n\t\t\t\t\tsignature: userInfo.signature || '',\n\t\t\t\t\tbirthday: userInfo.birthday || '',\n\t\t\t\t\tgender: userInfo.gender || 0,\n\t\t\t\t\tregion: userInfo.region || ''\n\t\t\t\t};\n\t\t\t\t// 保存原始数据\n\t\t\t\tthis.originalProfile = JSON.parse(JSON.stringify(this.userProfile));\n\t\t\t}\n\t\t},\n\n\t\t// 更换头像\n\t\tchangeAvatar() {\n\t\t\tuni.chooseImage({\n\t\t\t\tcount: 1,\n\t\t\t\tsizeType: ['compressed'],\n\t\t\t\tsourceType: ['album', 'camera'],\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tthis.userProfile.avatarUrl = res.tempFilePaths[0];\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '头像已更新',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 选择生日\n\t\tselectBirthday() {\n\t\t\tthis.showDatePicker = true;\n\t\t},\n\n\t\t// 日期改变\n\t\tonDateChange(e) {\n\t\t\tthis.userProfile.birthday = e.detail.value;\n\t\t\tthis.showDatePicker = false;\n\t\t},\n\n\t\t// 选择性别\n\t\tselectGender() {\n\t\t\tuni.showActionSheet({\n\t\t\t\titemList: ['男', '女', '不设置'],\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tthis.userProfile.gender = res.tapIndex === 2 ? 0 : res.tapIndex + 1;\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 选择地区\n\t\tselectRegion() {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '地区选择功能待完善',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\n\t\t// 保存资料\n\t\tsaveProfile() {\n\t\t\t// 验证必填项\n\t\t\tif (!this.userProfile.nickName.trim()) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请输入昵称',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// 保存到本地存储\n\t\t\tconst userInfo = uni.getStorageSync('userInfo') || {};\n\t\t\tconst updatedUserInfo = {\n\t\t\t\t...userInfo,\n\t\t\t\t...this.userProfile\n\t\t\t};\n\t\t\t\n\t\t\tuni.setStorageSync('userInfo', updatedUserInfo);\n\t\t\t\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '保存成功',\n\t\t\t\ticon: 'success'\n\t\t\t});\n\n\t\t\tsetTimeout(() => {\n\t\t\t\tuni.navigateBack();\n\t\t\t}, 1500);\n\t\t},\n\n\t\t// 取消编辑\n\t\tcancelEdit() {\n\t\t\t// 检查是否有修改\n\t\t\tconst hasChanges = JSON.stringify(this.userProfile) !== JSON.stringify(this.originalProfile);\n\t\t\t\n\t\t\tif (hasChanges) {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '确认取消',\n\t\t\t\t\tcontent: '您有未保存的修改，确定要取消吗？',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tuni.navigateBack();\n\t\t\t}\n\t\t}\n\t}\n};\n</script>\n\n<style scoped>\n.container {\n\tmin-height: 100vh;\n\tbackground: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);\n\tpadding: 30rpx;\n\tposition: relative;\n\toverflow: hidden;\n}\n\n/* 背景装饰 */\n.bg-decoration {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n\tpointer-events: none;\n\tz-index: 1;\n}\n\n.decoration-circle {\n\tposition: absolute;\n\tborder-radius: 50%;\n\tbackground: rgba(255, 255, 255, 0.1);\n\tanimation: float 6s ease-in-out infinite;\n}\n\n.circle-1 {\n\twidth: 180rpx;\n\theight: 180rpx;\n\ttop: 15%;\n\tright: -40rpx;\n\tanimation-delay: 0s;\n}\n\n.circle-2 {\n\twidth: 120rpx;\n\theight: 120rpx;\n\ttop: 70%;\n\tleft: -20rpx;\n\tanimation-delay: 2s;\n}\n\n.circle-3 {\n\twidth: 80rpx;\n\theight: 80rpx;\n\ttop: 40%;\n\tleft: 60%;\n\tanimation-delay: 4s;\n}\n\n@keyframes float {\n\t0%, 100% {\n\t\ttransform: translateY(0px) rotate(0deg);\n\t}\n\t50% {\n\t\ttransform: translateY(-15rpx) rotate(180deg);\n\t}\n}\n\n/* 头像编辑区域 */\n.avatar-section {\n\tposition: relative;\n\tz-index: 2;\n\tmargin-bottom: 30rpx;\n\tanimation: slideUp 0.8s ease-out;\n}\n\n.avatar-card {\n\tbackground: rgba(255, 255, 255, 0.95);\n\tborder-radius: 30rpx;\n\tpadding: 40rpx;\n\ttext-align: center;\n\tbox-shadow: 0 15rpx 35rpx rgba(255, 105, 180, 0.3);\n\tbackdrop-filter: blur(15rpx);\n}\n\n.avatar-container {\n\tposition: relative;\n\tdisplay: inline-block;\n\tmargin-bottom: 20rpx;\n}\n\n.profile-avatar {\n\twidth: 200rpx;\n\theight: 200rpx;\n\tborder-radius: 100rpx;\n\tborder: 4rpx solid #ff69b4;\n\tposition: relative;\n\tz-index: 2;\n}\n\n.avatar-border {\n\tposition: absolute;\n\ttop: -10rpx;\n\tleft: -10rpx;\n\twidth: 220rpx;\n\theight: 220rpx;\n\tborder: 2rpx solid rgba(255, 105, 180, 0.3);\n\tborder-radius: 110rpx;\n\tanimation: rotate 4s linear infinite;\n}\n\n.camera-icon {\n\tposition: absolute;\n\tbottom: 10rpx;\n\tright: 10rpx;\n\twidth: 50rpx;\n\theight: 50rpx;\n\tbackground: #ff69b4;\n\tborder-radius: 25rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 24rpx;\n\tcolor: white;\n\tbox-shadow: 0 4rpx 12rpx rgba(255, 105, 180, 0.4);\n\tz-index: 3;\n}\n\n.avatar-tip {\n\tfont-size: 24rpx;\n\tcolor: #ff69b4;\n\topacity: 0.8;\n}\n\n/* 表单区域 */\n.form-section {\n\tposition: relative;\n\tz-index: 2;\n\tmargin-bottom: 30rpx;\n\tanimation: slideUp 0.8s ease-out;\n}\n\n.form-section:nth-child(3) { animation-delay: 0.2s; }\n.form-section:nth-child(4) { animation-delay: 0.4s; }\n\n.section-header {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tmargin-bottom: 15rpx;\n\tpadding: 0 10rpx;\n}\n\n.section-title {\n\tfont-size: 32rpx;\n\tcolor: #ff1493;\n\tfont-weight: bold;\n}\n\n.section-icon {\n\tfont-size: 28rpx;\n}\n\n.form-card {\n\tbackground: rgba(255, 255, 255, 0.95);\n\tborder-radius: 25rpx;\n\tbackdrop-filter: blur(15rpx);\n\tbox-shadow: 0 10rpx 30rpx rgba(255, 105, 180, 0.25);\n\toverflow: hidden;\n}\n\n.form-item {\n\tpadding: 25rpx 30rpx;\n\tborder-bottom: 1rpx solid rgba(255, 105, 180, 0.1);\n\ttransition: all 0.3s ease;\n}\n\n.form-item:last-child {\n\tborder-bottom: none;\n}\n\n.form-item:active {\n\tbackground: rgba(255, 105, 180, 0.05);\n}\n\n.form-label {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 15rpx;\n}\n\n.label-icon {\n\tfont-size: 28rpx;\n\tmargin-right: 15rpx;\n\twidth: 35rpx;\n\ttext-align: center;\n}\n\n.label-text {\n\tfont-size: 28rpx;\n\tcolor: #ff1493;\n\tfont-weight: 500;\n}\n\n.form-input {\n\twidth: 100%;\n\tfont-size: 26rpx;\n\tcolor: #333;\n\tbackground: rgba(255, 255, 255, 0.8);\n\tborder: 2rpx solid rgba(255, 105, 180, 0.2);\n\tborder-radius: 15rpx;\n\tpadding: 20rpx;\n\tbox-sizing: border-box;\n}\n\n.form-input:focus {\n\tborder-color: #ff69b4;\n\tbackground: rgba(255, 255, 255, 0.95);\n}\n\n.form-textarea {\n\twidth: 100%;\n\theight: 120rpx;\n\tfont-size: 26rpx;\n\tcolor: #333;\n\tbackground: rgba(255, 255, 255, 0.8);\n\tborder: 2rpx solid rgba(255, 105, 180, 0.2);\n\tborder-radius: 15rpx;\n\tpadding: 20rpx;\n\tbox-sizing: border-box;\n\tresize: none;\n}\n\n.form-textarea:focus {\n\tborder-color: #ff69b4;\n\tbackground: rgba(255, 255, 255, 0.95);\n}\n\n.form-value {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 20rpx;\n\tbackground: rgba(255, 255, 255, 0.8);\n\tborder: 2rpx solid rgba(255, 105, 180, 0.2);\n\tborder-radius: 15rpx;\n}\n\n.value-text {\n\tfont-size: 26rpx;\n\tcolor: #333;\n\tflex: 1;\n}\n\n.form-arrow {\n\tfont-size: 20rpx;\n\tcolor: #ff69b4;\n\topacity: 0.6;\n}\n\n/* 操作按钮 */\n.action-section {\n\tposition: relative;\n\tz-index: 2;\n\tanimation: slideUp 0.8s ease-out 0.6s both;\n}\n\n.save-btn {\n\twidth: 100%;\n\tbackground: linear-gradient(135deg, #ff69b4, #ff1493);\n\tborder: none;\n\tborder-radius: 25rpx;\n\tpadding: 25rpx;\n\tmargin-bottom: 20rpx;\n\tbox-shadow: 0 8rpx 20rpx rgba(255, 20, 147, 0.3);\n\ttransition: all 0.3s ease;\n}\n\n.save-btn:active {\n\ttransform: scale(0.98);\n\tbox-shadow: 0 4rpx 15rpx rgba(255, 20, 147, 0.4);\n}\n\n.save-text {\n\tcolor: white;\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n}\n\n.cancel-btn {\n\twidth: 100%;\n\tbackground: rgba(255, 255, 255, 0.9);\n\tborder: 2rpx solid #ff69b4;\n\tborder-radius: 25rpx;\n\tpadding: 25rpx;\n\tbox-shadow: 0 8rpx 20rpx rgba(255, 105, 180, 0.2);\n\tbackdrop-filter: blur(10rpx);\n\ttransition: all 0.3s ease;\n}\n\n.cancel-btn:active {\n\ttransform: scale(0.98);\n\tbackground: rgba(255, 105, 180, 0.1);\n}\n\n.cancel-text {\n\tcolor: #ff1493;\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n}\n\n/* 动画效果 */\n@keyframes slideUp {\n\t0% {\n\t\ttransform: translateY(50rpx);\n\t\topacity: 0;\n\t}\n\t100% {\n\t\ttransform: translateY(0);\n\t\topacity: 1;\n\t}\n}\n\n@keyframes rotate {\n\t0% {\n\t\ttransform: rotate(0deg);\n\t}\n\t100% {\n\t\ttransform: rotate(360deg);\n\t}\n}\n</style>\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/auglar/text/pages/profile/edit-profile.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAuHA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,aAAa;AAAA,QACZ,WAAW;AAAA,QACX,UAAU;AAAA,QACV,WAAW;AAAA,QACX,UAAU;AAAA,QACV,QAAQ;AAAA;AAAA,QACR,QAAQ;AAAA,MACR;AAAA,MACD,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,iBAAiB,CAAG;AAAA;AAAA;EAErB;AAAA,EACD,UAAU;AAAA,IACT,aAAa;AACZ,YAAM,YAAY,EAAE,GAAG,OAAO,GAAG,KAAK,GAAG;AACzC,aAAO,UAAU,KAAK,YAAY,MAAM,KAAK;AAAA,IAC9C;AAAA,EACA;AAAA,EACD,SAAS;AACR,SAAK,gBAAe;AAAA,EACpB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,kBAAkB;AACjB,YAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAC9C,UAAI,UAAU;AACb,aAAK,cAAc;AAAA,UAClB,WAAW,SAAS,aAAa;AAAA,UACjC,UAAU,SAAS,YAAY;AAAA,UAC/B,WAAW,SAAS,aAAa;AAAA,UACjC,UAAU,SAAS,YAAY;AAAA,UAC/B,QAAQ,SAAS,UAAU;AAAA,UAC3B,QAAQ,SAAS,UAAU;AAAA;AAG5B,aAAK,kBAAkB,KAAK,MAAM,KAAK,UAAU,KAAK,WAAW,CAAC;AAAA,MACnE;AAAA,IACA;AAAA;AAAA,IAGD,eAAe;AACdA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,QACP,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AACjB,eAAK,YAAY,YAAY,IAAI,cAAc,CAAC;AAChDA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,iBAAiB;AAChB,WAAK,iBAAiB;AAAA,IACtB;AAAA;AAAA,IAGD,aAAa,GAAG;AACf,WAAK,YAAY,WAAW,EAAE,OAAO;AACrC,WAAK,iBAAiB;AAAA,IACtB;AAAA;AAAA,IAGD,eAAe;AACdA,oBAAAA,MAAI,gBAAgB;AAAA,QACnB,UAAU,CAAC,KAAK,KAAK,KAAK;AAAA,QAC1B,SAAS,CAAC,QAAQ;AACjB,eAAK,YAAY,SAAS,IAAI,aAAa,IAAI,IAAI,IAAI,WAAW;AAAA,QACnE;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AACdA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,cAAc;AAEb,UAAI,CAAC,KAAK,YAAY,SAAS,KAAI,GAAI;AACtCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAGA,YAAM,WAAWA,cAAG,MAAC,eAAe,UAAU,KAAK,CAAA;AACnD,YAAM,kBAAkB;AAAA,QACvB,GAAG;AAAA,QACH,GAAG,KAAK;AAAA;AAGTA,oBAAAA,MAAI,eAAe,YAAY,eAAe;AAE9CA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAED,iBAAW,MAAM;AAChBA,sBAAG,MAAC,aAAY;AAAA,MAChB,GAAE,IAAI;AAAA,IACP;AAAA;AAAA,IAGD,aAAa;AAEZ,YAAM,aAAa,KAAK,UAAU,KAAK,WAAW,MAAM,KAAK,UAAU,KAAK,eAAe;AAE3F,UAAI,YAAY;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,SAAS;AAAA,UACT,SAAS,CAAC,QAAQ;AACjB,gBAAI,IAAI,SAAS;AAChBA,4BAAG,MAAC,aAAY;AAAA,YACjB;AAAA,UACD;AAAA,QACD,CAAC;AAAA,aACK;AACNA,sBAAG,MAAC,aAAY;AAAA,MACjB;AAAA,IACD;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;AChQA,GAAG,WAAW,eAAe;"}