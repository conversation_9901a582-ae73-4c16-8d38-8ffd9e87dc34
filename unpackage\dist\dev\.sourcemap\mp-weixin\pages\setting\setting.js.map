{"version": 3, "file": "setting.js", "sources": ["pages/setting/setting.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvc2V0dGluZy9zZXR0aW5nLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 背景装饰 -->\n    <view class=\"bg-decoration\">\n      <view class=\"decoration-circle circle-1\"></view>\n      <view class=\"decoration-circle circle-2\"></view>\n      <view class=\"decoration-circle circle-3\"></view>\n    </view>\n\n    <!-- 个人设置 -->\n    <view class=\"setting-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">个人设置</text>\n        <text class=\"section-icon\">👤</text>\n      </view>\n      <view class=\"setting-card\">\n        <view class=\"setting-item\" @click=\"editProfile\">\n          <view class=\"setting-left\">\n            <text class=\"setting-icon\">📝</text>\n            <text class=\"setting-label\">编辑资料</text>\n          </view>\n          <view class=\"setting-right\">\n            <text class=\"setting-value\">修改头像、昵称</text>\n            <text class=\"setting-arrow\">→</text>\n          </view>\n        </view>\n        <view class=\"setting-item\" @tap=\"accountSecurity\">\n          <view class=\"setting-left\">\n            <text class=\"setting-icon\">🔒</text>\n            <text class=\"setting-label\">账号与安全</text>\n          </view>\n          <view class=\"setting-right\">\n            <text class=\"setting-value\">密码、绑定</text>\n            <text class=\"setting-arrow\">→</text>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 情侣关系设置 -->\n    <view class=\"setting-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">情侣关系</text>\n        <text class=\"section-icon\">💕</text>\n      </view>\n      <view class=\"setting-card\">\n        <view class=\"setting-item\" @tap=\"coupleConnection\">\n          <view class=\"setting-left\">\n            <text class=\"setting-icon\">🔗</text>\n            <text class=\"setting-label\">情侣连接</text>\n          </view>\n          <view class=\"setting-right\">\n            <text class=\"setting-value\">{{ coupleStatus }}</text>\n            <text class=\"setting-arrow\">→</text>\n          </view>\n        </view>\n        <view class=\"setting-item\" @tap=\"setLoveDate\">\n          <view class=\"setting-left\">\n            <text class=\"setting-icon\">📅</text>\n            <text class=\"setting-label\">恋爱开始日期</text>\n          </view>\n          <view class=\"setting-right\">\n            <text class=\"setting-value\">{{ loveStartDate }}</text>\n            <text class=\"setting-arrow\">→</text>\n          </view>\n        </view>\n        <view class=\"setting-item\" @tap=\"setCoupleNickname\">\n          <view class=\"setting-left\">\n            <text class=\"setting-icon\">💝</text>\n            <text class=\"setting-label\">情侣昵称</text>\n          </view>\n          <view class=\"setting-right\">\n            <text class=\"setting-value\">设置专属称呼</text>\n            <text class=\"setting-arrow\">→</text>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 隐私设置 -->\n    <view class=\"setting-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">隐私设置</text>\n        <text class=\"section-icon\">🛡️</text>\n      </view>\n      <view class=\"setting-card\">\n        <view class=\"setting-item\">\n          <view class=\"setting-left\">\n            <text class=\"setting-icon\">👁️</text>\n            <text class=\"setting-label\">动态可见性</text>\n          </view>\n          <view class=\"setting-right\">\n            <switch :checked=\"privacySettings.dynamicVisible\" @change=\"toggleDynamicVisible\" color=\"#ff69b4\" />\n          </view>\n        </view>\n        <view class=\"setting-item\">\n          <view class=\"setting-left\">\n            <text class=\"setting-icon\">📍</text>\n            <text class=\"setting-label\">位置共享</text>\n          </view>\n          <view class=\"setting-right\">\n            <switch :checked=\"privacySettings.locationShare\" @change=\"toggleLocationShare\" color=\"#ff69b4\" />\n          </view>\n        </view>\n        <view class=\"setting-item\">\n          <view class=\"setting-left\">\n            <text class=\"setting-icon\">🟢</text>\n            <text class=\"setting-label\">在线状态显示</text>\n          </view>\n          <view class=\"setting-right\">\n            <switch :checked=\"privacySettings.onlineStatus\" @change=\"toggleOnlineStatus\" color=\"#ff69b4\" />\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 通知设置 -->\n    <view class=\"setting-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">通知设置</text>\n        <text class=\"section-icon\">🔔</text>\n      </view>\n      <view class=\"setting-card\">\n        <view class=\"setting-item\">\n          <view class=\"setting-left\">\n            <text class=\"setting-icon\">💬</text>\n            <text class=\"setting-label\">消息通知</text>\n          </view>\n          <view class=\"setting-right\">\n            <switch :checked=\"notificationSettings.message\" @change=\"toggleMessageNotification\" color=\"#ff69b4\" />\n          </view>\n        </view>\n        <view class=\"setting-item\">\n          <view class=\"setting-left\">\n            <text class=\"setting-icon\">🎉</text>\n            <text class=\"setting-label\">纪念日提醒</text>\n          </view>\n          <view class=\"setting-right\">\n            <switch :checked=\"notificationSettings.anniversary\" @change=\"toggleAnniversaryNotification\" color=\"#ff69b4\" />\n          </view>\n        </view>\n        <view class=\"setting-item\">\n          <view class=\"setting-left\">\n            <text class=\"setting-icon\">💌</text>\n            <text class=\"setting-label\">每日情话</text>\n          </view>\n          <view class=\"setting-right\">\n            <switch :checked=\"notificationSettings.dailyQuote\" @change=\"toggleDailyQuoteNotification\" color=\"#ff69b4\" />\n          </view>\n        </view>\n        <view class=\"setting-item\">\n          <view class=\"setting-left\">\n            <text class=\"setting-icon\">📢</text>\n            <text class=\"setting-label\">系统通知</text>\n          </view>\n          <view class=\"setting-right\">\n            <switch :checked=\"notificationSettings.system\" @change=\"toggleSystemNotification\" color=\"#ff69b4\" />\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 应用设置 -->\n    <view class=\"setting-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">应用设置</text>\n        <text class=\"section-icon\">⚙️</text>\n      </view>\n      <view class=\"setting-card\">\n        <view class=\"setting-item\" @tap=\"setTheme\">\n          <view class=\"setting-left\">\n            <text class=\"setting-icon\">🎨</text>\n            <text class=\"setting-label\">主题设置</text>\n          </view>\n          <view class=\"setting-right\">\n            <text class=\"setting-value\">{{ currentTheme }}</text>\n            <text class=\"setting-arrow\">→</text>\n          </view>\n        </view>\n        <view class=\"setting-item\" @tap=\"setFontSize\">\n          <view class=\"setting-left\">\n            <text class=\"setting-icon\">🔤</text>\n            <text class=\"setting-label\">字体大小</text>\n          </view>\n          <view class=\"setting-right\">\n            <text class=\"setting-value\">{{ fontSize }}</text>\n            <text class=\"setting-arrow\">→</text>\n          </view>\n        </view>\n        <view class=\"setting-item\" @tap=\"clearCache\">\n          <view class=\"setting-left\">\n            <text class=\"setting-icon\">🗑️</text>\n            <text class=\"setting-label\">清除缓存</text>\n          </view>\n          <view class=\"setting-right\">\n            <text class=\"setting-value\">{{ cacheSize }}</text>\n            <text class=\"setting-arrow\">→</text>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 关于与帮助 -->\n    <view class=\"setting-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">关于与帮助</text>\n        <text class=\"section-icon\">❓</text>\n      </view>\n      <view class=\"setting-card\">\n        <view class=\"setting-item\" @tap=\"checkVersion\">\n          <view class=\"setting-left\">\n            <text class=\"setting-icon\">📱</text>\n            <text class=\"setting-label\">版本信息</text>\n          </view>\n          <view class=\"setting-right\">\n            <text class=\"setting-value\">v{{ appVersion }}</text>\n            <text class=\"setting-arrow\">→</text>\n          </view>\n        </view>\n        <view class=\"setting-item\" @tap=\"userAgreement\">\n          <view class=\"setting-left\">\n            <text class=\"setting-icon\">📄</text>\n            <text class=\"setting-label\">用户协议</text>\n          </view>\n          <view class=\"setting-right\">\n            <text class=\"setting-arrow\">→</text>\n          </view>\n        </view>\n        <view class=\"setting-item\" @tap=\"privacyPolicy\">\n          <view class=\"setting-left\">\n            <text class=\"setting-icon\">🔐</text>\n            <text class=\"setting-label\">隐私政策</text>\n          </view>\n          <view class=\"setting-right\">\n            <text class=\"setting-arrow\">→</text>\n          </view>\n        </view>\n        <view class=\"setting-item\" @tap=\"feedback\">\n          <view class=\"setting-left\">\n            <text class=\"setting-icon\">💭</text>\n            <text class=\"setting-label\">意见反馈</text>\n          </view>\n          <view class=\"setting-right\">\n            <text class=\"setting-arrow\">→</text>\n          </view>\n        </view>\n        <view class=\"setting-item\" @tap=\"contactService\">\n          <view class=\"setting-left\">\n            <text class=\"setting-icon\">🎧</text>\n            <text class=\"setting-label\">联系客服</text>\n          </view>\n          <view class=\"setting-right\">\n            <text class=\"setting-arrow\">→</text>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 退出登录 -->\n    <view class=\"logout-section\">\n      <button class=\"logout-btn\" @tap=\"logout\">\n        <text class=\"logout-text\">退出登录</text>\n      </button>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      coupleStatus: '已连接',\n      loveStartDate: '2023-08-04',\n      currentTheme: '浪漫粉色',\n      fontSize: '标准',\n      cacheSize: '12.5MB',\n      appVersion: '1.0.0',\n      privacySettings: {\n        dynamicVisible: true,\n        locationShare: false,\n        onlineStatus: true\n      },\n      notificationSettings: {\n        message: true,\n        anniversary: true,\n        dailyQuote: true,\n        system: false\n      }\n    };\n  },\n  methods: {\n    // 个人设置\n    editProfile() {\r\n\t\t if(uni.getStorageSync('userInfo')){\r\n\t\t    uni.navigateTo({\r\n\t\t      url:'/pages/profile/edit-profile'\r\n\t\t    })\r\n\t\t  }else{  // 修正：将 })else 改为 }else\r\n\t\t    uni.navigateTo({\r\n\t\t      url:'/pages/index/index'\r\n\t\t    })\r\n\t\t  }\n    },\n    accountSecurity() {\n      uni.showToast({ title: '账号与安全功能待开发', icon: 'none' });\n    },\n\n    // 情侣关系设置\n    coupleConnection() {\n      uni.showToast({ title: '情侣连接功能待开发', icon: 'none' });\n    },\n    setLoveDate() {\n      uni.showToast({ title: '设置恋爱日期功能待开发', icon: 'none' });\n    },\n    setCoupleNickname() {\n      uni.showToast({ title: '设置情侣昵称功能待开发', icon: 'none' });\n    },\n\n    // 隐私设置开关\n    toggleDynamicVisible(e) {\n      this.privacySettings.dynamicVisible = e.detail.value;\n      uni.showToast({\n        title: e.detail.value ? '已开启动态可见' : '已关闭动态可见',\n        icon: 'none'\n      });\n    },\n    toggleLocationShare(e) {\n      this.privacySettings.locationShare = e.detail.value;\n      uni.showToast({\n        title: e.detail.value ? '已开启位置共享' : '已关闭位置共享',\n        icon: 'none'\n      });\n    },\n    toggleOnlineStatus(e) {\n      this.privacySettings.onlineStatus = e.detail.value;\n      uni.showToast({\n        title: e.detail.value ? '已显示在线状态' : '已隐藏在线状态',\n        icon: 'none'\n      });\n    },\n\n    // 通知设置开关\n    toggleMessageNotification(e) {\n      this.notificationSettings.message = e.detail.value;\n      uni.showToast({\n        title: e.detail.value ? '已开启消息通知' : '已关闭消息通知',\n        icon: 'none'\n      });\n    },\n    toggleAnniversaryNotification(e) {\n      this.notificationSettings.anniversary = e.detail.value;\n      uni.showToast({\n        title: e.detail.value ? '已开启纪念日提醒' : '已关闭纪念日提醒',\n        icon: 'none'\n      });\n    },\n    toggleDailyQuoteNotification(e) {\n      this.notificationSettings.dailyQuote = e.detail.value;\n      uni.showToast({\n        title: e.detail.value ? '已开启每日情话' : '已关闭每日情话',\n        icon: 'none'\n      });\n    },\n    toggleSystemNotification(e) {\n      this.notificationSettings.system = e.detail.value;\n      uni.showToast({\n        title: e.detail.value ? '已开启系统通知' : '已关闭系统通知',\n        icon: 'none'\n      });\n    },\n\n    // 应用设置\n    setTheme() {\n      uni.showActionSheet({\n        itemList: ['浪漫粉色', '清新蓝色', '温暖橙色', '优雅紫色'],\n        success: (res) => {\n          const themes = ['浪漫粉色', '清新蓝色', '温暖橙色', '优雅紫色'];\n          this.currentTheme = themes[res.tapIndex];\n          uni.showToast({ title: `已切换到${this.currentTheme}主题`, icon: 'none' });\n        }\n      });\n    },\n    setFontSize() {\n      uni.showActionSheet({\n        itemList: ['小', '标准', '大', '超大'],\n        success: (res) => {\n          const sizes = ['小', '标准', '大', '超大'];\n          this.fontSize = sizes[res.tapIndex];\n          uni.showToast({ title: `字体大小已设置为${this.fontSize}`, icon: 'none' });\n        }\n      });\n    },\n    clearCache() {\n      uni.showModal({\n        title: '清除缓存',\n        content: '确定要清除所有缓存数据吗？',\n        success: (res) => {\n          if (res.confirm) {\n            this.cacheSize = '0MB';\n            uni.showToast({ title: '缓存清除成功', icon: 'success' });\n          }\n        }\n      });\n    },\n\n    // 关于与帮助\n    checkVersion() {\n      uni.showToast({ title: '已是最新版本', icon: 'success' });\n    },\n    userAgreement() {\n      uni.showToast({ title: '用户协议功能待开发', icon: 'none' });\n    },\n    privacyPolicy() {\n      uni.showToast({ title: '隐私政策功能待开发', icon: 'none' });\n    },\n    feedback() {\n      uni.showToast({ title: '意见反馈功能待开发', icon: 'none' });\n    },\n    contactService() {\n      uni.showToast({ title: '联系客服功能待开发', icon: 'none' });\n    },\n\n    // 退出登录\n    logout() {\n      uni.showModal({\n        title: '确认退出',\n        content: '确定要退出登录吗？',\n        success: (res) => {\n          if (res.confirm) {\n            uni.showToast({ title: '已退出登录', icon: 'success' });\n            setTimeout(() => {\n              uni.switchTab({ url: '/pages/index/index' });\n            }, 1500);\n          }\n        }\n      });\n    }\n  }\n};\n</script>\n\n<style scoped>\n.container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);\n  padding: 30rpx;\n  position: relative;\n  overflow: hidden;\n}\n\n/* 背景装饰 */\n.bg-decoration {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  pointer-events: none;\n  z-index: 1;\n}\n\n.decoration-circle {\n  position: absolute;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.1);\n  animation: float 6s ease-in-out infinite;\n}\n\n.circle-1 {\n  width: 180rpx;\n  height: 180rpx;\n  top: 15%;\n  right: -40rpx;\n  animation-delay: 0s;\n}\n\n.circle-2 {\n  width: 120rpx;\n  height: 120rpx;\n  top: 70%;\n  left: -20rpx;\n  animation-delay: 2s;\n}\n\n.circle-3 {\n  width: 80rpx;\n  height: 80rpx;\n  top: 40%;\n  left: 60%;\n  animation-delay: 4s;\n}\n\n@keyframes float {\n  0%, 100% {\n    transform: translateY(0px) rotate(0deg);\n  }\n  50% {\n    transform: translateY(-15rpx) rotate(180deg);\n  }\n}\n\n/* 设置区域 */\n.setting-section {\n  position: relative;\n  z-index: 2;\n  margin-bottom: 30rpx;\n  animation: slideUp 0.8s ease-out;\n}\n\n.setting-section:nth-child(2) { animation-delay: 0.1s; }\n.setting-section:nth-child(3) { animation-delay: 0.2s; }\n.setting-section:nth-child(4) { animation-delay: 0.3s; }\n.setting-section:nth-child(5) { animation-delay: 0.4s; }\n.setting-section:nth-child(6) { animation-delay: 0.5s; }\n.setting-section:nth-child(7) { animation-delay: 0.6s; }\n\n.section-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 15rpx;\n  padding: 0 10rpx;\n}\n\n.section-title {\n  font-size: 32rpx;\n  color: #ff1493;\n  font-weight: bold;\n}\n\n.section-icon {\n  font-size: 28rpx;\n}\n\n.setting-card {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 25rpx;\n  backdrop-filter: blur(15rpx);\n  box-shadow: 0 10rpx 30rpx rgba(255, 105, 180, 0.25);\n  overflow: hidden;\n}\n\n/* 设置项 */\n.setting-item {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 25rpx 30rpx;\n  border-bottom: 1rpx solid rgba(255, 105, 180, 0.1);\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.setting-item:last-child {\n  border-bottom: none;\n}\n\n.setting-item:active {\n  background: rgba(255, 105, 180, 0.05);\n  transform: scale(0.98);\n}\n\n.setting-item::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\n  transition: left 0.5s ease;\n}\n\n.setting-item:active::before {\n  left: 100%;\n}\n\n.setting-left {\n  display: flex;\n  align-items: center;\n  flex: 1;\n}\n\n.setting-icon {\n  font-size: 32rpx;\n  margin-right: 20rpx;\n  width: 40rpx;\n  text-align: center;\n}\n\n.setting-label {\n  font-size: 28rpx;\n  color: #ff1493;\n  font-weight: 500;\n}\n\n.setting-right {\n  display: flex;\n  align-items: center;\n}\n\n.setting-value {\n  font-size: 24rpx;\n  color: #ff69b4;\n  opacity: 0.8;\n  margin-right: 10rpx;\n}\n\n.setting-arrow {\n  font-size: 20rpx;\n  color: #ff69b4;\n  opacity: 0.6;\n}\n\n/* 退出登录 */\n.logout-section {\n  position: relative;\n  z-index: 2;\n  animation: slideUp 0.8s ease-out 0.7s both;\n}\n\n.logout-btn {\n  width: 100%;\n  background: rgba(255, 255, 255, 0.9);\n  border: 2rpx solid #ff69b4;\n  border-radius: 25rpx;\n  padding: 25rpx;\n  box-shadow: 0 8rpx 20rpx rgba(255, 105, 180, 0.2);\n  backdrop-filter: blur(10rpx);\n  transition: all 0.3s ease;\n}\n\n.logout-btn:active {\n  transform: scale(0.98);\n  background: rgba(255, 105, 180, 0.1);\n  box-shadow: 0 4rpx 15rpx rgba(255, 105, 180, 0.3);\n}\n\n.logout-text {\n  color: #ff1493;\n  font-size: 28rpx;\n  font-weight: 600;\n}\n\n/* 动画效果 */\n@keyframes slideUp {\n  0% {\n    transform: translateY(50rpx);\n    opacity: 0;\n  }\n  100% {\n    transform: translateY(0);\n    opacity: 1;\n  }\n}\n</style>\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/auglar/text/pages/setting/setting.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA4QA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,cAAc;AAAA,MACd,eAAe;AAAA,MACf,cAAc;AAAA,MACd,UAAU;AAAA,MACV,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,iBAAiB;AAAA,QACf,gBAAgB;AAAA,QAChB,eAAe;AAAA,QACf,cAAc;AAAA,MACf;AAAA,MACD,sBAAsB;AAAA,QACpB,SAAS;AAAA,QACT,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,MACV;AAAA;EAEH;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,cAAc;AACf,UAAGA,cAAG,MAAC,eAAe,UAAU,GAAE;AAC/BA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAI;AAAA,SACL;AAAA,aACE;AACHA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAI;AAAA,SACL;AAAA,MACH;AAAA,IACC;AAAA,IACD,kBAAkB;AAChBA,oBAAG,MAAC,UAAU,EAAE,OAAO,cAAc,MAAM,OAAO,CAAC;AAAA,IACpD;AAAA;AAAA,IAGD,mBAAmB;AACjBA,oBAAG,MAAC,UAAU,EAAE,OAAO,aAAa,MAAM,OAAK,CAAG;AAAA,IACnD;AAAA,IACD,cAAc;AACZA,oBAAG,MAAC,UAAU,EAAE,OAAO,eAAe,MAAM,OAAO,CAAC;AAAA,IACrD;AAAA,IACD,oBAAoB;AAClBA,oBAAG,MAAC,UAAU,EAAE,OAAO,eAAe,MAAM,OAAO,CAAC;AAAA,IACrD;AAAA;AAAA,IAGD,qBAAqB,GAAG;AACtB,WAAK,gBAAgB,iBAAiB,EAAE,OAAO;AAC/CA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,EAAE,OAAO,QAAQ,YAAY;AAAA,QACpC,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,oBAAoB,GAAG;AACrB,WAAK,gBAAgB,gBAAgB,EAAE,OAAO;AAC9CA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,EAAE,OAAO,QAAQ,YAAY;AAAA,QACpC,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,mBAAmB,GAAG;AACpB,WAAK,gBAAgB,eAAe,EAAE,OAAO;AAC7CA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,EAAE,OAAO,QAAQ,YAAY;AAAA,QACpC,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,0BAA0B,GAAG;AAC3B,WAAK,qBAAqB,UAAU,EAAE,OAAO;AAC7CA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,EAAE,OAAO,QAAQ,YAAY;AAAA,QACpC,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,8BAA8B,GAAG;AAC/B,WAAK,qBAAqB,cAAc,EAAE,OAAO;AACjDA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,EAAE,OAAO,QAAQ,aAAa;AAAA,QACrC,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,6BAA6B,GAAG;AAC9B,WAAK,qBAAqB,aAAa,EAAE,OAAO;AAChDA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,EAAE,OAAO,QAAQ,YAAY;AAAA,QACpC,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,yBAAyB,GAAG;AAC1B,WAAK,qBAAqB,SAAS,EAAE,OAAO;AAC5CA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,EAAE,OAAO,QAAQ,YAAY;AAAA,QACpC,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,WAAW;AACTA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QACzC,SAAS,CAAC,QAAQ;AAChB,gBAAM,SAAS,CAAC,QAAQ,QAAQ,QAAQ,MAAM;AAC9C,eAAK,eAAe,OAAO,IAAI,QAAQ;AACvCA,wBAAAA,MAAI,UAAU,EAAE,OAAO,OAAO,KAAK,YAAY,MAAM,MAAM,OAAQ,CAAA;AAAA,QACrE;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,cAAc;AACZA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,KAAK,MAAM,KAAK,IAAI;AAAA,QAC/B,SAAS,CAAC,QAAQ;AAChB,gBAAM,QAAQ,CAAC,KAAK,MAAM,KAAK,IAAI;AACnC,eAAK,WAAW,MAAM,IAAI,QAAQ;AAClCA,wBAAAA,MAAI,UAAU,EAAE,OAAO,WAAW,KAAK,QAAQ,IAAI,MAAM,OAAQ,CAAA;AAAA,QACnE;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,aAAa;AACXA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,iBAAK,YAAY;AACjBA,0BAAG,MAAC,UAAU,EAAE,OAAO,UAAU,MAAM,UAAQ,CAAG;AAAA,UACpD;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,eAAe;AACbA,oBAAG,MAAC,UAAU,EAAE,OAAO,UAAU,MAAM,UAAQ,CAAG;AAAA,IACnD;AAAA,IACD,gBAAgB;AACdA,oBAAG,MAAC,UAAU,EAAE,OAAO,aAAa,MAAM,OAAK,CAAG;AAAA,IACnD;AAAA,IACD,gBAAgB;AACdA,oBAAG,MAAC,UAAU,EAAE,OAAO,aAAa,MAAM,OAAK,CAAG;AAAA,IACnD;AAAA,IACD,WAAW;AACTA,oBAAG,MAAC,UAAU,EAAE,OAAO,aAAa,MAAM,OAAK,CAAG;AAAA,IACnD;AAAA,IACD,iBAAiB;AACfA,oBAAG,MAAC,UAAU,EAAE,OAAO,aAAa,MAAM,OAAK,CAAG;AAAA,IACnD;AAAA;AAAA,IAGD,SAAS;AACPA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,0BAAG,MAAC,UAAU,EAAE,OAAO,SAAS,MAAM,UAAQ,CAAG;AACjD,uBAAW,MAAM;AACfA,4BAAAA,MAAI,UAAU,EAAE,KAAK,qBAAsB,CAAA;AAAA,YAC5C,GAAE,IAAI;AAAA,UACT;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrbA,GAAG,WAAW,eAAe;"}