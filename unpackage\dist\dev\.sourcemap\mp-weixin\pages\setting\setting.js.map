{"version": 3, "file": "setting.js", "sources": ["pages/setting/setting.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvc2V0dGluZy9zZXR0aW5nLnZ1ZQ"], "sourcesContent": ["<template>\n\t<view class=\"setting-container\">\n\t\t<!-- 页面标题 -->\n\t\t<view class=\"page-header\">\n\t\t\t<text class=\"page-title\">设置</text>\n\t\t</view>\n\t\t\n\t\t<!-- 账户信息卡片 -->\n\t\t<view class=\"account-card\">\n\t\t\t<view class=\"account-info\">\n\t\t\t\t<image class=\"account-avatar\" src=\"/static/logo.png\" mode=\"aspectFill\"></image>\n\t\t\t\t<view class=\"account-details\">\n\t\t\t\t\t<text class=\"account-name\">甜蜜情侣</text>\n\t\t\t\t\t<text class=\"account-phone\">138****8888</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"vip-badge\">VIP</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 设置分组 -->\n\t\t<view class=\"setting-group\">\n\t\t\t<text class=\"group-title\">账户设置</text>\n\t\t\t<view class=\"setting-list\">\n\t\t\t\t<view class=\"setting-item\">\n\t\t\t\t\t<view class=\"item-left\">\n\t\t\t\t\t\t<view class=\"item-icon\">👤</view>\n\t\t\t\t\t\t<text class=\"item-text\">个人资料</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-right\">\n\t\t\t\t\t\t<text class=\"item-arrow\">></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"setting-item\">\n\t\t\t\t\t<view class=\"item-left\">\n\t\t\t\t\t\t<view class=\"item-icon\">🔒</view>\n\t\t\t\t\t\t<text class=\"item-text\">隐私设置</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-right\">\n\t\t\t\t\t\t<text class=\"item-arrow\">></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"setting-item\">\n\t\t\t\t\t<view class=\"item-left\">\n\t\t\t\t\t\t<view class=\"item-icon\">🔑</view>\n\t\t\t\t\t\t<text class=\"item-text\">修改密码</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-right\">\n\t\t\t\t\t\t<text class=\"item-arrow\">></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 应用设置 -->\n\t\t<view class=\"setting-group\">\n\t\t\t<text class=\"group-title\">应用设置</text>\n\t\t\t<view class=\"setting-list\">\n\t\t\t\t<view class=\"setting-item\">\n\t\t\t\t\t<view class=\"item-left\">\n\t\t\t\t\t\t<view class=\"item-icon\">🔔</view>\n\t\t\t\t\t\t<text class=\"item-text\">消息通知</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-right\">\n\t\t\t\t\t\t<switch class=\"setting-switch\" checked=\"true\" color=\"#ff6b9d\" />\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"setting-item\">\n\t\t\t\t\t<view class=\"item-left\">\n\t\t\t\t\t\t<view class=\"item-icon\">🌙</view>\n\t\t\t\t\t\t<text class=\"item-text\">夜间模式</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-right\">\n\t\t\t\t\t\t<switch class=\"setting-switch\" color=\"#ff6b9d\" />\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"setting-item\">\n\t\t\t\t\t<view class=\"item-left\">\n\t\t\t\t\t\t<view class=\"item-icon\">🔊</view>\n\t\t\t\t\t\t<text class=\"item-text\">声音效果</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-right\">\n\t\t\t\t\t\t<switch class=\"setting-switch\" checked=\"true\" color=\"#ff6b9d\" />\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 情侣设置 -->\n\t\t<view class=\"setting-group\">\n\t\t\t<text class=\"group-title\">情侣设置</text>\n\t\t\t<view class=\"setting-list\">\n\t\t\t\t<view class=\"setting-item\">\n\t\t\t\t\t<view class=\"item-left\">\n\t\t\t\t\t\t<view class=\"item-icon\">💕</view>\n\t\t\t\t\t\t<text class=\"item-text\">恋爱纪念日</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-right\">\n\t\t\t\t\t\t<text class=\"item-value\">2023-02-14</text>\n\t\t\t\t\t\t<text class=\"item-arrow\">></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"setting-item\">\n\t\t\t\t\t<view class=\"item-left\">\n\t\t\t\t\t\t<view class=\"item-icon\">🎂</view>\n\t\t\t\t\t\t<text class=\"item-text\">生日提醒</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-right\">\n\t\t\t\t\t\t<switch class=\"setting-switch\" checked=\"true\" color=\"#ff6b9d\" />\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"setting-item\">\n\t\t\t\t\t<view class=\"item-left\">\n\t\t\t\t\t\t<view class=\"item-icon\">📱</view>\n\t\t\t\t\t\t<text class=\"item-text\">数据同步</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-right\">\n\t\t\t\t\t\t<text class=\"item-arrow\">></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 其他设置 -->\n\t\t<view class=\"setting-group\">\n\t\t\t<text class=\"group-title\">其他</text>\n\t\t\t<view class=\"setting-list\">\n\t\t\t\t<view class=\"setting-item\">\n\t\t\t\t\t<view class=\"item-left\">\n\t\t\t\t\t\t<view class=\"item-icon\">📋</view>\n\t\t\t\t\t\t<text class=\"item-text\">用户协议</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-right\">\n\t\t\t\t\t\t<text class=\"item-arrow\">></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"setting-item\">\n\t\t\t\t\t<view class=\"item-left\">\n\t\t\t\t\t\t<view class=\"item-icon\">🛡️</view>\n\t\t\t\t\t\t<text class=\"item-text\">隐私政策</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-right\">\n\t\t\t\t\t\t<text class=\"item-arrow\">></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"setting-item\">\n\t\t\t\t\t<view class=\"item-left\">\n\t\t\t\t\t\t<view class=\"item-icon\">ℹ️</view>\n\t\t\t\t\t\t<text class=\"item-text\">关于我们</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-right\">\n\t\t\t\t\t\t<text class=\"item-value\">v1.0.0</text>\n\t\t\t\t\t\t<text class=\"item-arrow\">></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 退出登录 -->\n\t\t<view class=\"logout-section\">\n\t\t\t<button class=\"logout-btn\" @tap=\"handleLogout\">\n\t\t\t\t退出登录\n\t\t\t</button>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\thandleLogout() {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '确认退出',\n\t\t\t\t\tcontent: '确定要退出登录吗？',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '已退出登录',\n\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\tuni.reLaunch({\n\t\t\t\t\t\t\t\t\turl: '/pages/login/login'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}, 1500);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style scoped>\n\t.setting-container {\n\t\tmin-height: 100vh;\n\t\tbackground: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);\n\t\tpadding: 40rpx 30rpx;\n\t}\n\t\n\t/* 页面标题 */\n\t.page-header {\n\t\ttext-align: center;\n\t\tmargin-bottom: 40rpx;\n\t}\n\t\n\t.page-title {\n\t\tfont-size: 40rpx;\n\t\tfont-weight: 700;\n\t\tcolor: #fff;\n\t\ttext-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);\n\t}\n\t\n\t/* 账户信息卡片 */\n\t.account-card {\n\t\tbackground: rgba(255,255,255,0.95);\n\t\tborder-radius: 24rpx;\n\t\tpadding: 40rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tbox-shadow: 0 8rpx 24rpx rgba(255,154,158,0.2);\n\t\tbackdrop-filter: blur(10rpx);\n\t}\n\t\n\t.account-info {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\t\n\t.account-avatar {\n\t\twidth: 100rpx;\n\t\theight: 100rpx;\n\t\tborder-radius: 50rpx;\n\t\tborder: 3rpx solid #ff6b9d;\n\t\tmargin-right: 30rpx;\n\t}\n\t\n\t.account-details {\n\t\tflex: 1;\n\t}\n\t\n\t.account-name {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #333;\n\t\tdisplay: block;\n\t\tmargin-bottom: 8rpx;\n\t}\n\t\n\t.account-phone {\n\t\tfont-size: 26rpx;\n\t\tcolor: #999;\n\t}\n\t\n\t.vip-badge {\n\t\tbackground: linear-gradient(135deg, #ffd700, #ffb347);\n\t\tcolor: #fff;\n\t\tfont-size: 20rpx;\n\t\tfont-weight: 600;\n\t\tpadding: 8rpx 16rpx;\n\t\tborder-radius: 20rpx;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(255,215,0,0.3);\n\t}\n\t\n\t/* 设置分组 */\n\t.setting-group {\n\t\tmargin-bottom: 30rpx;\n\t}\n\t\n\t.group-title {\n\t\tfont-size: 28rpx;\n\t\tfont-weight: 600;\n\t\tcolor: rgba(255,255,255,0.9);\n\t\tmargin-bottom: 20rpx;\n\t\tmargin-left: 20rpx;\n\t\tdisplay: block;\n\t}\n\t\n\t.setting-list {\n\t\tbackground: rgba(255,255,255,0.95);\n\t\tborder-radius: 24rpx;\n\t\toverflow: hidden;\n\t\tbox-shadow: 0 8rpx 24rpx rgba(255,154,158,0.2);\n\t\tbackdrop-filter: blur(10rpx);\n\t}\n\t\n\t.setting-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 40rpx;\n\t\tborder-bottom: 1rpx solid rgba(0,0,0,0.05);\n\t\ttransition: background-color 0.3s ease;\n\t}\n\t\n\t.setting-item:last-child {\n\t\tborder-bottom: none;\n\t}\n\t\n\t.setting-item:active {\n\t\tbackground-color: rgba(255,107,157,0.05);\n\t}\n\t\n\t.item-left {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tflex: 1;\n\t}\n\t\n\t.item-icon {\n\t\tfont-size: 32rpx;\n\t\tmargin-right: 24rpx;\n\t\twidth: 40rpx;\n\t\ttext-align: center;\n\t}\n\t\n\t.item-text {\n\t\tfont-size: 30rpx;\n\t\tcolor: #333;\n\t\tfont-weight: 500;\n\t}\n\t\n\t.item-right {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\t\n\t.item-value {\n\t\tfont-size: 26rpx;\n\t\tcolor: #999;\n\t\tmargin-right: 16rpx;\n\t}\n\t\n\t.item-arrow {\n\t\tfont-size: 24rpx;\n\t\tcolor: #ccc;\n\t}\n\t\n\t.setting-switch {\n\t\ttransform: scale(0.8);\n\t}\n\t\n\t/* 退出登录 */\n\t.logout-section {\n\t\tmargin-top: 60rpx;\n\t\ttext-align: center;\n\t}\n\t\n\t.logout-btn {\n\t\twidth: 100%;\n\t\tbackground: rgba(255,255,255,0.95);\n\t\tcolor: #ff6b9d;\n\t\tborder: 2rpx solid #ff6b9d;\n\t\tborder-radius: 50rpx;\n\t\tpadding: 32rpx;\n\t\tfont-size: 30rpx;\n\t\tfont-weight: 600;\n\t\tbox-shadow: 0 6rpx 20rpx rgba(255,154,158,0.15);\n\t\ttransition: all 0.3s ease;\n\t}\n\t\n\t.logout-btn:active {\n\t\tbackground: rgba(255,107,157,0.1);\n\t\ttransform: translateY(2rpx);\n\t}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/auglar/text/pages/setting/setting.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAuKC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO,CAEP;AAAA,EACA;AAAA,EACD,SAAS;AAAA,IACR,eAAe;AACdA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAChBA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,YACP,CAAC;AACD,uBAAW,MAAM;AAChBA,4BAAAA,MAAI,SAAS;AAAA,gBACZ,KAAK;AAAA,cACN,CAAC;AAAA,YACD,GAAE,IAAI;AAAA,UACR;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;ACjMD,GAAG,WAAW,eAAe;"}