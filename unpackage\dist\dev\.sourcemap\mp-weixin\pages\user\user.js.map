{"version": 3, "file": "user.js", "sources": ["pages/user/user.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlci91c2VyLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"user-container\">\r\n\t\t<!-- 个人信息卡片 -->\r\n\t\t<view class=\"profile-card\">\r\n\t\t\t<view class=\"profile-header\">\r\n\t\t\t\t<view class=\"avatar-section\">\r\n\t\t\t\t\t<image class=\"user-avatar\" src=\"\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t<view class=\"online-dot\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"user-info\">\r\n\t\t\t\t\t<text class=\"username\">{{userInfo.nickName}}</text>\r\n\t\t\t\t\t<text class=\"user-status\">在线</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<button class=\"edit-btn\" @tap=\"navigateTo('/pages/profile/edit-profile')\">\r\n\t\t\t\t\t编辑\r\n\t\t\t\t</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 情侣关系卡片 -->\r\n\t\t<view class=\"relationship-card\">\r\n\t\t\t<view class=\"relationship-header\">\r\n\t\t\t\t<text class=\"card-title\">我们的关系</text>\r\n\t\t\t\t<text class=\"love-status\">热恋中 💕</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"couple-info\">\r\n\t\t\t\t<view class=\"couple-avatar-section\">\r\n\t\t\t\t\t<image class=\"couple-avatar\" src=\"/static/logo.png\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t<text class=\"couple-name\">小甜心</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"love-connection\">\r\n\t\t\t\t\t<view class=\"love-line\"></view>\r\n\t\t\t\t\t<view class=\"love-heart\">💖</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"couple-avatar-section\">\r\n\t\t\t\t\t<image class=\"couple-avatar\" src=\"/static/logo.png\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t<text class=\"couple-name\">小宝贝</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"love-stats\">\r\n\t\t\t\t<view class=\"stat-item\">\r\n\t\t\t\t\t<text class=\"stat-number\">520</text>\r\n\t\t\t\t\t<text class=\"stat-label\">相恋天数</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"stat-item\">\r\n\t\t\t\t\t<text class=\"stat-number\">1314</text>\r\n\t\t\t\t\t<text class=\"stat-label\">甜蜜时刻</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"stat-item\">\r\n\t\t\t\t\t<text class=\"stat-number\">99+</text>\r\n\t\t\t\t\t<text class=\"stat-label\">共同回忆</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 功能菜单 */\r\n\t\t<view class=\"menu-grid\">\r\n\t\t\t<view class=\"menu-item\" @tap=\"navigateTo('/pages/profile/edit-profile')\">\r\n\t\t\t\t<view class=\"menu-icon\">👤</view>\r\n\t\t\t\t<text class=\"menu-text\">编辑资料</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"menu-item\">\r\n\t\t\t\t<view class=\"menu-icon\">📷</view>\r\n\t\t\t\t<text class=\"menu-text\">相册</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"menu-item\">\r\n\t\t\t\t<view class=\"menu-icon\">💌</view>\r\n\t\t\t\t<text class=\"menu-text\">情书</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"menu-item\">\r\n\t\t\t\t<view class=\"menu-icon\">🎂</view>\r\n\t\t\t\t<text class=\"menu-text\">纪念日</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"menu-item\">\r\n\t\t\t\t<view class=\"menu-icon\">🎁</view>\r\n\t\t\t\t<text class=\"menu-text\">礼物</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"menu-item\" @tap=\"navigateTo('/pages/setting/setting')\">\r\n\t\t\t\t<view class=\"menu-icon\">⚙️</view>\r\n\t\t\t\t<text class=\"menu-text\">设置</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 退出登录 -->\r\n\t\t<view class=\"logout-section\">\r\n\t\t\t<button class=\"logout-btn\" @tap=\"handleLogout\">\r\n\t\t\t\t退出登录\r\n\t\t\t</button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tuserInfo:null,\r\n\t\t\t\tisLogin:false\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t//退出登录\r\n\t\t\thandleLogout() {\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\ttitle:\"退出登录\",\r\n\t\t\t\tcontent: '请确认是否要退出?',\r\n\t\t\t\tsuccess:(res)=>{\r\n\t\t\t\t\tif(res.confirm){\r\n\t\t\t\t\t\tuni.removeStorageSync('userInfo')\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle:'已退出登录',\r\n\t\t\t\t\t\t\ticon:'success',\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl:\"/pages/login/login\"\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t},fail:(err)=>{\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle:'退出登录失败',\r\n\t\t\t\t\t\ticon:'none',\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\t})\r\n\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\t//检查登录状态\r\n\t\t\tcheckLoginStatus() {\r\n\t\t\t            const userInfo = uni.getStorageSync('userInfo');\r\n\t\t\t            if (userInfo) {\r\n\t\t\t                this.userInfo = userInfo;\r\n\t\t\t                this.isLogin = true;\r\n\t\t\t            } else {\r\n\t\t\t                this.userInfo = null;\r\n\t\t\t                this.isLogin = false;\r\n\t\t\t            }\r\n\t\t\t        }\r\n\t\t\t  \r\n\t\t},\r\n\t\tonShow(){\r\n\t\t\tthis.checkLoginStatus()\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped>\r\n\t.user-container {\r\n\t\tmin-height: 100vh;\r\n\t\tbackground: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);\r\n\t\tpadding: 40rpx 30rpx;\r\n\t}\r\n\t\r\n\t/* 个人信息卡片 */\r\n\t.profile-card {\r\n\t\tbackground: rgba(255,255,255,0.95);\r\n\t\tborder-radius: 24rpx;\r\n\t\tpadding: 40rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\tbox-shadow: 0 8rpx 24rpx rgba(255,154,158,0.2);\r\n\t\tbackdrop-filter: blur(10rpx);\r\n\t}\r\n\t\r\n\t.profile-header {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\t\r\n\t.avatar-section {\r\n\t\tposition: relative;\r\n\t\tmargin-right: 30rpx;\r\n\t}\r\n\t\r\n\t.user-avatar {\r\n\t\twidth: 120rpx;\r\n\t\theight: 120rpx;\r\n\t\tborder-radius: 60rpx;\r\n\t\tborder: 4rpx solid #ff6b9d;\r\n\t}\r\n\t\r\n\t.online-dot {\r\n\t\tposition: absolute;\r\n\t\tbottom: 5rpx;\r\n\t\tright: 5rpx;\r\n\t\twidth: 24rpx;\r\n\t\theight: 24rpx;\r\n\t\tbackground: #4caf50;\r\n\t\tborder: 3rpx solid #fff;\r\n\t\tborder-radius: 50%;\r\n\t}\r\n\t\r\n\t.user-info {\r\n\t\tflex: 1;\r\n\t}\r\n\t\r\n\t.username {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333;\r\n\t\tdisplay: block;\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\t\r\n\t.user-status {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #4caf50;\r\n\t}\r\n\t\r\n\t.edit-btn {\r\n\t\tbackground: linear-gradient(135deg, #ff6b9d, #c44569);\r\n\t\tcolor: #fff;\r\n\t\tborder: none;\r\n\t\tborder-radius: 40rpx;\r\n\t\tpadding: 16rpx 32rpx;\r\n\t\tfont-size: 26rpx;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\t\r\n\t/* 情侣关系卡片 */\r\n\t.relationship-card {\r\n\t\tbackground: rgba(255,255,255,0.95);\r\n\t\tborder-radius: 24rpx;\r\n\t\tpadding: 40rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\tbox-shadow: 0 8rpx 24rpx rgba(255,154,158,0.2);\r\n\t\tbackdrop-filter: blur(10rpx);\r\n\t}\r\n\t\r\n\t.relationship-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\t\r\n\t.card-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333;\r\n\t}\r\n\t\r\n\t.love-status {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #ff6b9d;\r\n\t\tbackground: rgba(255,107,157,0.1);\r\n\t\tpadding: 8rpx 16rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t}\r\n\t\r\n\t.couple-info {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tmargin-bottom: 40rpx;\r\n\t}\r\n\t\r\n\t.couple-avatar-section {\r\n\t\ttext-align: center;\r\n\t\tflex: 1;\r\n\t}\r\n\t\r\n\t.couple-avatar {\r\n\t\twidth: 80rpx;\r\n\t\theight: 80rpx;\r\n\t\tborder-radius: 40rpx;\r\n\t\tborder: 3rpx solid #ff6b9d;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\t\r\n\t.couple-name {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666;\r\n\t\tdisplay: block;\r\n\t}\r\n\t\r\n\t.love-connection {\r\n\t\tflex: 1;\r\n\t\tposition: relative;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\t\r\n\t.love-line {\r\n\t\twidth: 80rpx;\r\n\t\theight: 2rpx;\r\n\t\tbackground: #ff6b9d;\r\n\t}\r\n\t\r\n\t.love-heart {\r\n\t\tposition: absolute;\r\n\t\tfont-size: 24rpx;\r\n\t\tanimation: pulse 2s infinite;\r\n\t}\r\n\t\r\n\t.love-stats {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-around;\r\n\t\tbackground: rgba(255,107,157,0.05);\r\n\t\tborder-radius: 16rpx;\r\n\t\tpadding: 30rpx;\r\n\t}\r\n\t\r\n\t.stat-item {\r\n\t\ttext-align: center;\r\n\t}\r\n\t\r\n\t.stat-number {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 700;\r\n\t\tcolor: #ff6b9d;\r\n\t\tdisplay: block;\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\t\r\n\t.stat-label {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\t\r\n\t/* 功能菜单 */\r\n\t.menu-grid {\r\n\t\tdisplay: grid;\r\n\t\tgrid-template-columns: repeat(3, 1fr);\r\n\t\tgap: 20rpx;\r\n\t\tmargin-bottom: 40rpx;\r\n\t}\r\n\t\r\n\t.menu-item {\r\n\t\tbackground: rgba(255,255,255,0.95);\r\n\t\tborder-radius: 20rpx;\r\n\t\tpadding: 40rpx 20rpx;\r\n\t\ttext-align: center;\r\n\t\tbox-shadow: 0 6rpx 20rpx rgba(255,154,158,0.15);\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\t\r\n\t.menu-item:active {\r\n\t\ttransform: scale(0.95);\r\n\t\tbox-shadow: 0 4rpx 16rpx rgba(255,154,158,0.25);\r\n\t}\r\n\t\r\n\t.menu-icon {\r\n\t\tfont-size: 40rpx;\r\n\t\tmargin-bottom: 16rpx;\r\n\t\tdisplay: block;\r\n\t}\r\n\t\r\n\t.menu-text {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\t\r\n\t/* 退出登录 */\r\n\t.logout-section {\r\n\t\ttext-align: center;\r\n\t}\r\n\t\r\n\t.logout-btn {\r\n\t\twidth: 100%;\r\n\t\tbackground: rgba(255,255,255,0.95);\r\n\t\tcolor: #ff6b9d;\r\n\t\tborder: 2rpx solid #ff6b9d;\r\n\t\tborder-radius: 50rpx;\r\n\t\tpadding: 32rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: 600;\r\n\t\tbox-shadow: 0 6rpx 20rpx rgba(255,154,158,0.15);\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\t\r\n\t.logout-btn:active {\r\n\t\tbackground: rgba(255,107,157,0.1);\r\n\t\ttransform: translateY(2rpx);\r\n\t}\r\n\t\r\n\t/* 动画 */\r\n\t@keyframes pulse {\r\n\t\t0%, 100% {\r\n\t\t\ttransform: scale(1);\r\n\t\t}\r\n\t\t50% {\r\n\t\t\ttransform: scale(1.1);\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/auglar/text/pages/user/user.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AA8FC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,UAAS;AAAA,MACT,SAAQ;AAAA,IACT;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA,IAER,eAAe;AACdA,oBAAAA,MAAI,UAAU;AAAA,QACd,OAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAQ,CAAC,QAAM;AACd,cAAG,IAAI,SAAQ;AACdA,0BAAG,MAAC,kBAAkB,UAAU;AAChCA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAM;AAAA,cACN,MAAK;AAAA,aACL;AACDA,0BAAAA,MAAI,WAAW;AAAA,cACd,KAAI;AAAA,aACJ;AAAA,UACF;AAAA,QACD;AAAA,QAAE,MAAK,CAAC,QAAM;AACbA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAM;AAAA,YACN,MAAK;AAAA,WACL;AAAA,QACF;AAAA,OACC;AAAA,IAGD;AAAA;AAAA,IAED,mBAAmB;AACP,YAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAC9C,UAAI,UAAU;AACV,aAAK,WAAW;AAChB,aAAK,UAAU;AAAA,aACZ;AACH,aAAK,WAAW;AAChB,aAAK,UAAU;AAAA,MACnB;AAAA,IACJ;AAAA,EAER;AAAA,EACD,SAAQ;AACP,SAAK,iBAAiB;AAAA,EACvB;AACD;;;;;;;;;;;AC/ID,GAAG,WAAW,eAAe;"}