
.container.data-v-1cf27b2a {
		min-height: 100vh;
		background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
		padding: 40rpx 30rpx;
		position: relative;
}
	
	/* 顶部装饰 */
.header-decoration.data-v-1cf27b2a {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 200rpx;
		pointer-events: none;
}
.floating-heart.data-v-1cf27b2a {
		position: absolute;
		font-size: 40rpx;
		opacity: 0.6;
		animation: float-1cf27b2a 4s ease-in-out infinite;
}
.heart1.data-v-1cf27b2a {
		top: 60rpx;
		left: 10%;
		animation-delay: 0s;
}
.heart2.data-v-1cf27b2a {
		top: 100rpx;
		right: 15%;
		animation-delay: 1.5s;
}
.heart3.data-v-1cf27b2a {
		top: 140rpx;
		left: 50%;
		animation-delay: 3s;
}
	
	/* 主标题 */
.title-section.data-v-1cf27b2a {
		text-align: center;
		margin: 120rpx 0 80rpx;
}
.main-title.data-v-1cf27b2a {
		font-size: 56rpx;
		font-weight: 700;
		color: #fff;
		text-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
		display: block;
		margin-bottom: 20rpx;
}
.sub-title.data-v-1cf27b2a {
		font-size: 28rpx;
		color: rgba(255,255,255,0.9);
		font-weight: 400;
}
	
	/* 功能卡片 */
.feature-cards.data-v-1cf27b2a {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 30rpx;
		margin-bottom: 80rpx;
}
.feature-card.data-v-1cf27b2a {
		background: rgba(255,255,255,0.95);
		border-radius: 24rpx;
		padding: 40rpx 30rpx;
		text-align: center;
		box-shadow: 0 8rpx 24rpx rgba(255,154,158,0.2);
		transition: all 0.3s ease;
		-webkit-backdrop-filter: blur(10rpx);
		        backdrop-filter: blur(10rpx);
}
.feature-card.data-v-1cf27b2a:active {
		transform: scale(0.95);
		box-shadow: 0 4rpx 12rpx rgba(255,154,158,0.3);
}
.card-icon.data-v-1cf27b2a {
		font-size: 48rpx;
		margin-bottom: 20rpx;
		display: block;
}
.card-title.data-v-1cf27b2a {
		font-size: 30rpx;
		font-weight: 600;
		color: #333;
		display: block;
		margin-bottom: 10rpx;
}
.card-desc.data-v-1cf27b2a {
		font-size: 24rpx;
		color: #999;
		line-height: 1.4;
}
	
	/* 登录提示 */
.login-prompt.data-v-1cf27b2a {
		text-align: center;
		background: rgba(255,255,255,0.9);
		border-radius: 24rpx;
		padding: 40rpx;
		box-shadow: 0 8rpx 24rpx rgba(255,154,158,0.2);
}
.prompt-text.data-v-1cf27b2a {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 30rpx;
		display: block;
}
.login-btn.data-v-1cf27b2a {
		background: linear-gradient(135deg, #ff6b9d, #c44569);
		color: #fff;
		border: none;
		border-radius: 50rpx;
		padding: 24rpx 60rpx;
		font-size: 30rpx;
		font-weight: 600;
		box-shadow: 0 6rpx 20rpx rgba(196,69,105,0.3);
		transition: all 0.3s ease;
}
.login-btn.data-v-1cf27b2a:active {
		transform: translateY(2rpx);
		box-shadow: 0 4rpx 16rpx rgba(196,69,105,0.4);
}
	
	/* 动画 */
@keyframes float-1cf27b2a {
0%, 100% {
			transform: translateY(0px);
}
50% {
			transform: translateY(-20rpx);
}
}
