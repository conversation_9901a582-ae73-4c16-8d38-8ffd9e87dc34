"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      verifyCode: "",
      isAgreed: false,
      countdown: 0,
      userInfo: null,
      isLoading: false
    };
  },
  methods: {
    // 微信登录
    async handleWechatLogin() {
      common_vendor.index.__f__("log", "at pages/login/login.vue:66", "登录点击拉");
      if (!this.isAgreed) {
        common_vendor.index.showToast({
          title: "请先同意用户协议",
          icon: "none"
        });
        return;
      }
      if (!common_vendor.index.getStorageInfoSync("userInfo")) {
        try {
          common_vendor.index.showLoading({ title: "登录中..." });
          const userProfileRes = await new Promise((resolve, reject) => {
            common_vendor.index.getUserProfile({
              desc: "登录后可以同步数据",
              success: resolve,
              fail: reject
            });
          }, 1e3);
          const userInfo = userProfileRes.userInfo;
          this.userInfo = userInfo;
          const loginRes = await new Promise((resolve, reject) => {
            common_vendor.index.login({
              provider: "weixin",
              success: resolve,
              fail: reject
            });
          }, 1e3);
          const code = loginRes.code;
          if (!code) {
            common_vendor.index.showToast({
              title: "获取code失败",
              icon: "none"
            });
            return;
          }
          const saveRes = await common_vendor.nr.callFunction({
            name: "save-user-infor",
            data: {
              "nickname": userInfo.nickname,
              "avatarUrl": userInfo.avatarUrl,
              "gender": userInfo.gender,
              "code": code
            }
          }, 1e3);
          common_vendor.index.setStorageSync("userInfo", this.userInfo);
          common_vendor.index.showToast({
            title: "登录成功",
            icon: "success"
          });
          common_vendor.index.switchTab({
            url: "/pages/index/index"
          });
          common_vendor.index.__f__("log", "at pages/login/login.vue:120", userInfo);
        } catch (err) {
          common_vendor.index.showToast({
            title: "登录失败",
            icon: "none"
          });
        }
      } else {
        common_vendor.index.showToast({
          title: "登录成功",
          icon: "success"
        });
        common_vendor.index.switchTab({
          url: "/pages/index/index"
        });
      }
    },
    //勾选用户协议
    onAgreementChange() {
      this.isAgreed = true;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.handleWechatLogin && $options.handleWechatLogin(...args)),
    b: $data.isAgreed,
    c: common_vendor.o((...args) => $options.onAgreementChange && $options.onAgreementChange(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-e4e4508d"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/login/login.js.map
