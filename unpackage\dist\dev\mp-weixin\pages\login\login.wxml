<view class="login-container data-v-e4e4508d"><view class="top-decoration data-v-e4e4508d"><view class="love-icon data-v-e4e4508d">💕</view></view><view class="login-card data-v-e4e4508d"><view class="logo-section data-v-e4e4508d"><view class="app-logo data-v-e4e4508d">💑</view><text class="app-name data-v-e4e4508d">甜蜜时光</text><text class="app-slogan data-v-e4e4508d">记录我们的爱情故事</text></view><button class="wechat-login-btn data-v-e4e4508d" bindtap="{{a}}"><view class="btn-content data-v-e4e4508d"><text class="wechat-icon data-v-e4e4508d">💚</text><text class="btn-text data-v-e4e4508d">微信一键登录</text></view></button><view class="agreement-section data-v-e4e4508d"><view class="agreement-check data-v-e4e4508d"><checkbox-group class="data-v-e4e4508d" bindchange="{{c}}"><checkbox class="data-v-e4e4508d" value="agree" checked="{{b}}" color="#ff6b9d"/></checkbox-group><text class="agreement-text data-v-e4e4508d"> 我已阅读并同意 <text class="link-text data-v-e4e4508d">《用户协议》</text> 和 <text class="link-text data-v-e4e4508d">《隐私政策》</text></text></view></view><view class="bottom-decoration data-v-e4e4508d"><view class="heart-animation data-v-e4e4508d"><text class="heart data-v-e4e4508d">💖</text><text class="heart data-v-e4e4508d">💕</text><text class="heart data-v-e4e4508d">💝</text></view></view></view></view>