
.login-container.data-v-e4e4508d {
		min-height: 100vh;
		background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
		padding: 40rpx;
		position: relative;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
}
	
	/* 顶部装饰 */
.top-decoration.data-v-e4e4508d {
		text-align: center;
		margin-bottom: 60rpx;
}
.love-icon.data-v-e4e4508d {
		font-size: 80rpx;
		animation: pulse-e4e4508d 2s infinite;
}
	
	/* 登录卡片 */
.login-card.data-v-e4e4508d {
		background: rgba(255,255,255,0.95);
		border-radius: 32rpx;
		padding: 60rpx 40rpx;
		box-shadow: 0 12rpx 40rpx rgba(255,154,158,0.25);
		-webkit-backdrop-filter: blur(10rpx);
		        backdrop-filter: blur(10rpx);
}
	
	/* Logo区域 */
.logo-section.data-v-e4e4508d {
		text-align: center;
		margin-bottom: 60rpx;
}
.app-logo.data-v-e4e4508d {
		font-size: 100rpx;
		margin-bottom: 20rpx;
		display: block;
}
.app-name.data-v-e4e4508d {
		font-size: 48rpx;
		font-weight: 700;
		color: #333;
		display: block;
		margin-bottom: 10rpx;
}
.app-slogan.data-v-e4e4508d {
		font-size: 28rpx;
		color: #999;
}
	
	/* 微信登录按钮 */
.wechat-login-btn.data-v-e4e4508d {
		width: 100%;
		background: #07c160;
		border: none;
		border-radius: 50rpx;
		padding: 32rpx;
		margin-bottom: 40rpx;
		box-shadow: 0 8rpx 24rpx rgba(7,193,96,0.3);
		transition: all 0.3s ease;
}
.wechat-login-btn.data-v-e4e4508d:active {
		transform: translateY(2rpx);
		box-shadow: 0 6rpx 20rpx rgba(7,193,96,0.4);
}
.btn-content.data-v-e4e4508d {
		display: flex;
		align-items: center;
		justify-content: center;
}
.wechat-icon.data-v-e4e4508d {
		font-size: 36rpx;
		margin-right: 16rpx;
}
.btn-text.data-v-e4e4508d {
		font-size: 32rpx;
		color: #fff;
		font-weight: 600;
}
	
	/* 手机号登录 */
.phone-login-section.data-v-e4e4508d {
		border-top: 1rpx solid #f0f0f0;
		padding-top: 40rpx;
}
.section-title.data-v-e4e4508d {
		text-align: center;
		font-size: 28rpx;
		color: #999;
		margin-bottom: 40rpx;
		display: block;
}
.input-group.data-v-e4e4508d {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
		background: #f8f8f8;
		border-radius: 50rpx;
		padding: 0 30rpx;
		height: 100rpx;
}
.phone-input.data-v-e4e4508d, .code-input.data-v-e4e4508d {
		flex: 1;
		font-size: 30rpx;
		color: #333;
		background: transparent;
		border: none;
}
.get-code-btn.data-v-e4e4508d {
		background: linear-gradient(135deg, #ff6b9d, #c44569);
		color: #fff;
		border: none;
		border-radius: 40rpx;
		padding: 16rpx 32rpx;
		font-size: 26rpx;
		font-weight: 500;
}
.phone-login-btn.data-v-e4e4508d {
		width: 100%;
		background: linear-gradient(135deg, #ff6b9d, #c44569);
		color: #fff;
		border: none;
		border-radius: 50rpx;
		padding: 32rpx;
		font-size: 32rpx;
		font-weight: 600;
		box-shadow: 0 8rpx 24rpx rgba(196,69,105,0.3);
		transition: all 0.3s ease;
}
.phone-login-btn.data-v-e4e4508d:active {
		transform: translateY(2rpx);
		box-shadow: 0 6rpx 20rpx rgba(196,69,105,0.4);
}
	
	/* 用户协议 */
.agreement-section.data-v-e4e4508d {
		margin-top: 40rpx;
		text-align: center;
}
.agreement-check.data-v-e4e4508d {
		display: flex;
		align-items: center;
		justify-content: center;
}
.agreement-text.data-v-e4e4508d {
		font-size: 24rpx;
		color: #999;
		margin-left: 16rpx;
}
.link-text.data-v-e4e4508d {
		color: #ff6b9d;
}
	
	/* 底部装饰 */
.bottom-decoration.data-v-e4e4508d {
		position: fixed;
		bottom: 60rpx;
		left: 50%;
		transform: translateX(-50%);
}
.heart-animation.data-v-e4e4508d {
		display: flex;
		gap: 20rpx;
}
.heart.data-v-e4e4508d {
		font-size: 32rpx;
		opacity: 0.6;
		animation: bounce-e4e4508d 2s infinite;
}
.heart.data-v-e4e4508d:nth-child(2) {
		animation-delay: 0.5s;
}
.heart.data-v-e4e4508d:nth-child(3) {
		animation-delay: 1s;
}
	
	/* 动画 */
@keyframes pulse-e4e4508d {
0%, 100% {
			transform: scale(1);
}
50% {
			transform: scale(1.1);
}
}
@keyframes bounce-e4e4508d {
0%, 100% {
			transform: translateY(0);
}
50% {
			transform: translateY(-10rpx);
}
}
