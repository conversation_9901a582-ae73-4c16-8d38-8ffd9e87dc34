"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      userProfile: {
        avatarUrl: "",
        nickName: "",
        signature: "",
        birthday: "",
        gender: 0,
        // 0:未知 1:男 2:女
        region: ""
      },
      defaultAvatar: "/static/default-avatar.png",
      showDatePicker: false,
      originalProfile: {}
      // 保存原始数据用于取消操作
    };
  },
  computed: {
    genderText() {
      const genderMap = { 0: "未设置", 1: "男", 2: "女" };
      return genderMap[this.userProfile.gender] || "未设置";
    }
  },
  onLoad() {
    this.loadUserProfile();
  },
  methods: {
    // 加载用户资料
    loadUserProfile() {
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      if (userInfo) {
        this.userProfile = {
          avatarUrl: userInfo.avatarUrl || "",
          nickName: userInfo.nickName || "",
          signature: userInfo.signature || "",
          birthday: userInfo.birthday || "",
          gender: userInfo.gender || 0,
          region: userInfo.region || ""
        };
        this.originalProfile = JSON.parse(JSON.stringify(this.userProfile));
      }
    },
    // 更换头像
    changeAvatar() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          this.userProfile.avatarUrl = res.tempFilePaths[0];
          common_vendor.index.showToast({
            title: "头像已更新",
            icon: "success"
          });
        }
      });
    },
    // 选择生日
    selectBirthday() {
      this.showDatePicker = true;
    },
    // 日期改变
    onDateChange(e) {
      this.userProfile.birthday = e.detail.value;
      this.showDatePicker = false;
    },
    // 选择性别
    selectGender() {
      common_vendor.index.showActionSheet({
        itemList: ["男", "女", "不设置"],
        success: (res) => {
          this.userProfile.gender = res.tapIndex === 2 ? 0 : res.tapIndex + 1;
        }
      });
    },
    // 选择地区
    selectRegion() {
      common_vendor.index.showToast({
        title: "地区选择功能待完善",
        icon: "none"
      });
    },
    // 保存资料
    saveProfile() {
      if (!this.userProfile.nickName.trim()) {
        common_vendor.index.showToast({
          title: "请输入昵称",
          icon: "none"
        });
        return;
      }
      const userInfo = common_vendor.index.getStorageSync("userInfo") || {};
      const updatedUserInfo = {
        ...userInfo,
        ...this.userProfile
      };
      common_vendor.index.setStorageSync("userInfo", updatedUserInfo);
      common_vendor.index.showToast({
        title: "保存成功",
        icon: "success"
      });
      setTimeout(() => {
        common_vendor.index.navigateBack();
      }, 1500);
    },
    // 取消编辑
    cancelEdit() {
      const hasChanges = JSON.stringify(this.userProfile) !== JSON.stringify(this.originalProfile);
      if (hasChanges) {
        common_vendor.index.showModal({
          title: "确认取消",
          content: "您有未保存的修改，确定要取消吗？",
          success: (res) => {
            if (res.confirm) {
              common_vendor.index.navigateBack();
            }
          }
        });
      } else {
        common_vendor.index.navigateBack();
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.userProfile.avatarUrl || $data.defaultAvatar,
    b: common_vendor.o((...args) => $options.changeAvatar && $options.changeAvatar(...args)),
    c: $data.userProfile.nickName,
    d: common_vendor.o(($event) => $data.userProfile.nickName = $event.detail.value),
    e: $data.userProfile.signature,
    f: common_vendor.o(($event) => $data.userProfile.signature = $event.detail.value),
    g: common_vendor.t($data.userProfile.birthday || "请选择生日"),
    h: common_vendor.o((...args) => $options.selectBirthday && $options.selectBirthday(...args)),
    i: common_vendor.t($options.genderText),
    j: common_vendor.o((...args) => $options.selectGender && $options.selectGender(...args)),
    k: common_vendor.t($data.userProfile.region || "请选择地区"),
    l: common_vendor.o((...args) => $options.selectRegion && $options.selectRegion(...args)),
    m: common_vendor.o((...args) => $options.saveProfile && $options.saveProfile(...args)),
    n: common_vendor.o((...args) => $options.cancelEdit && $options.cancelEdit(...args)),
    o: $data.showDatePicker
  }, $data.showDatePicker ? {
    p: $data.userProfile.birthday,
    q: common_vendor.o((...args) => $options.onDateChange && $options.onDateChange(...args)),
    r: common_vendor.o(($event) => $data.showDatePicker = false)
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-4438b7d4"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/profile/edit-profile.js.map
