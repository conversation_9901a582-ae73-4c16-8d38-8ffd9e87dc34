
.container.data-v-4438b7d4 {
	min-height: 100vh;
	background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
	padding: 30rpx;
	position: relative;
	overflow: hidden;
}

/* 背景装饰 */
.bg-decoration.data-v-4438b7d4 {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
	z-index: 1;
}
.decoration-circle.data-v-4438b7d4 {
	position: absolute;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.1);
	animation: float-4438b7d4 6s ease-in-out infinite;
}
.circle-1.data-v-4438b7d4 {
	width: 180rpx;
	height: 180rpx;
	top: 15%;
	right: -40rpx;
	animation-delay: 0s;
}
.circle-2.data-v-4438b7d4 {
	width: 120rpx;
	height: 120rpx;
	top: 70%;
	left: -20rpx;
	animation-delay: 2s;
}
.circle-3.data-v-4438b7d4 {
	width: 80rpx;
	height: 80rpx;
	top: 40%;
	left: 60%;
	animation-delay: 4s;
}
@keyframes float-4438b7d4 {
0%, 100% {
		transform: translateY(0px) rotate(0deg);
}
50% {
		transform: translateY(-15rpx) rotate(180deg);
}
}

/* 头像编辑区域 */
.avatar-section.data-v-4438b7d4 {
	position: relative;
	z-index: 2;
	margin-bottom: 30rpx;
	animation: slideUp-4438b7d4 0.8s ease-out;
}
.avatar-card.data-v-4438b7d4 {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 30rpx;
	padding: 40rpx;
	text-align: center;
	box-shadow: 0 15rpx 35rpx rgba(255, 105, 180, 0.3);
	-webkit-backdrop-filter: blur(15rpx);
	        backdrop-filter: blur(15rpx);
}
.avatar-container.data-v-4438b7d4 {
	position: relative;
	display: inline-block;
	margin-bottom: 20rpx;
}
.profile-avatar.data-v-4438b7d4 {
	width: 200rpx;
	height: 200rpx;
	border-radius: 100rpx;
	border: 4rpx solid #ff69b4;
	position: relative;
	z-index: 2;
}
.avatar-border.data-v-4438b7d4 {
	position: absolute;
	top: -10rpx;
	left: -10rpx;
	width: 220rpx;
	height: 220rpx;
	border: 2rpx solid rgba(255, 105, 180, 0.3);
	border-radius: 110rpx;
	animation: rotate-4438b7d4 4s linear infinite;
}
.camera-icon.data-v-4438b7d4 {
	position: absolute;
	bottom: 10rpx;
	right: 10rpx;
	width: 50rpx;
	height: 50rpx;
	background: #ff69b4;
	border-radius: 25rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	color: white;
	box-shadow: 0 4rpx 12rpx rgba(255, 105, 180, 0.4);
	z-index: 3;
}
.avatar-tip.data-v-4438b7d4 {
	font-size: 24rpx;
	color: #ff69b4;
	opacity: 0.8;
}

/* 表单区域 */
.form-section.data-v-4438b7d4 {
	position: relative;
	z-index: 2;
	margin-bottom: 30rpx;
	animation: slideUp-4438b7d4 0.8s ease-out;
}
.form-section.data-v-4438b7d4:nth-child(3) { animation-delay: 0.2s;
}
.form-section.data-v-4438b7d4:nth-child(4) { animation-delay: 0.4s;
}
.section-header.data-v-4438b7d4 {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 15rpx;
	padding: 0 10rpx;
}
.section-title.data-v-4438b7d4 {
	font-size: 32rpx;
	color: #ff1493;
	font-weight: bold;
}
.section-icon.data-v-4438b7d4 {
	font-size: 28rpx;
}
.form-card.data-v-4438b7d4 {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 25rpx;
	-webkit-backdrop-filter: blur(15rpx);
	        backdrop-filter: blur(15rpx);
	box-shadow: 0 10rpx 30rpx rgba(255, 105, 180, 0.25);
	overflow: hidden;
}
.form-item.data-v-4438b7d4 {
	padding: 25rpx 30rpx;
	border-bottom: 1rpx solid rgba(255, 105, 180, 0.1);
	transition: all 0.3s ease;
}
.form-item.data-v-4438b7d4:last-child {
	border-bottom: none;
}
.form-item.data-v-4438b7d4:active {
	background: rgba(255, 105, 180, 0.05);
}
.form-label.data-v-4438b7d4 {
	display: flex;
	align-items: center;
	margin-bottom: 15rpx;
}
.label-icon.data-v-4438b7d4 {
	font-size: 28rpx;
	margin-right: 15rpx;
	width: 35rpx;
	text-align: center;
}
.label-text.data-v-4438b7d4 {
	font-size: 28rpx;
	color: #ff1493;
	font-weight: 500;
}
.form-input.data-v-4438b7d4 {
	width: 100%;
	font-size: 26rpx;
	color: #333;
	background: rgba(255, 255, 255, 0.8);
	border: 2rpx solid rgba(255, 105, 180, 0.2);
	border-radius: 15rpx;
	padding: 20rpx;
	box-sizing: border-box;
}
.form-input.data-v-4438b7d4:focus {
	border-color: #ff69b4;
	background: rgba(255, 255, 255, 0.95);
}
.form-textarea.data-v-4438b7d4 {
	width: 100%;
	height: 120rpx;
	font-size: 26rpx;
	color: #333;
	background: rgba(255, 255, 255, 0.8);
	border: 2rpx solid rgba(255, 105, 180, 0.2);
	border-radius: 15rpx;
	padding: 20rpx;
	box-sizing: border-box;
	resize: none;
}
.form-textarea.data-v-4438b7d4:focus {
	border-color: #ff69b4;
	background: rgba(255, 255, 255, 0.95);
}
.form-value.data-v-4438b7d4 {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx;
	background: rgba(255, 255, 255, 0.8);
	border: 2rpx solid rgba(255, 105, 180, 0.2);
	border-radius: 15rpx;
}
.value-text.data-v-4438b7d4 {
	font-size: 26rpx;
	color: #333;
	flex: 1;
}
.form-arrow.data-v-4438b7d4 {
	font-size: 20rpx;
	color: #ff69b4;
	opacity: 0.6;
}

/* 操作按钮 */
.action-section.data-v-4438b7d4 {
	position: relative;
	z-index: 2;
	animation: slideUp-4438b7d4 0.8s ease-out 0.6s both;
}
.save-btn.data-v-4438b7d4 {
	width: 100%;
	background: linear-gradient(135deg, #ff69b4, #ff1493);
	border: none;
	border-radius: 25rpx;
	padding: 25rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 8rpx 20rpx rgba(255, 20, 147, 0.3);
	transition: all 0.3s ease;
}
.save-btn.data-v-4438b7d4:active {
	transform: scale(0.98);
	box-shadow: 0 4rpx 15rpx rgba(255, 20, 147, 0.4);
}
.save-text.data-v-4438b7d4 {
	color: white;
	font-size: 28rpx;
	font-weight: 600;
}
.cancel-btn.data-v-4438b7d4 {
	width: 100%;
	background: rgba(255, 255, 255, 0.9);
	border: 2rpx solid #ff69b4;
	border-radius: 25rpx;
	padding: 25rpx;
	box-shadow: 0 8rpx 20rpx rgba(255, 105, 180, 0.2);
	-webkit-backdrop-filter: blur(10rpx);
	        backdrop-filter: blur(10rpx);
	transition: all 0.3s ease;
}
.cancel-btn.data-v-4438b7d4:active {
	transform: scale(0.98);
	background: rgba(255, 105, 180, 0.1);
}
.cancel-text.data-v-4438b7d4 {
	color: #ff1493;
	font-size: 28rpx;
	font-weight: 600;
}

/* 动画效果 */
@keyframes slideUp-4438b7d4 {
0% {
		transform: translateY(50rpx);
		opacity: 0;
}
100% {
		transform: translateY(0);
		opacity: 1;
}
}
@keyframes rotate-4438b7d4 {
0% {
		transform: rotate(0deg);
}
100% {
		transform: rotate(360deg);
}
}
