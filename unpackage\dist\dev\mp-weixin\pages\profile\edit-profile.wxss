
.edit-profile-container.data-v-4438b7d4 {
		min-height: 100vh;
		background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
		padding: 40rpx 30rpx;
}
	
	/* 页面标题 */
.page-header.data-v-4438b7d4 {
		text-align: center;
		margin-bottom: 40rpx;
}
.page-title.data-v-4438b7d4 {
		font-size: 40rpx;
		font-weight: 700;
		color: #fff;
		text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);
}
	
	/* 头像编辑 */
.avatar-section.data-v-4438b7d4 {
		text-align: center;
		background: rgba(255,255,255,0.95);
		border-radius: 24rpx;
		padding: 60rpx 40rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 8rpx 24rpx rgba(255,154,158,0.2);
		-webkit-backdrop-filter: blur(10rpx);
		        backdrop-filter: blur(10rpx);
}
.avatar-container.data-v-4438b7d4 {
		position: relative;
		display: inline-block;
		margin-bottom: 20rpx;
}
.profile-avatar.data-v-4438b7d4 {
		width: 160rpx;
		height: 160rpx;
		border-radius: 80rpx;
		border: 4rpx solid #ff6b9d;
}
.avatar-edit-overlay.data-v-4438b7d4 {
		position: absolute;
		bottom: -8rpx;
		right: -8rpx;
		width: 48rpx;
		height: 48rpx;
		background: #ff6b9d;
		border-radius: 24rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 4rpx 12rpx rgba(255,107,157,0.3);
}
.camera-icon.data-v-4438b7d4 {
		font-size: 20rpx;
		color: #fff;
}
.avatar-tip.data-v-4438b7d4 {
		font-size: 24rpx;
		color: #999;
}
	
	/* 表单区域 */
.form-section.data-v-4438b7d4 {
		margin-bottom: 30rpx;
}
.section-title.data-v-4438b7d4 {
		font-size: 28rpx;
		font-weight: 600;
		color: rgba(255,255,255,0.9);
		margin-bottom: 20rpx;
		margin-left: 20rpx;
		display: block;
}
.form-list.data-v-4438b7d4 {
		background: rgba(255,255,255,0.95);
		border-radius: 24rpx;
		overflow: hidden;
		box-shadow: 0 8rpx 24rpx rgba(255,154,158,0.2);
		-webkit-backdrop-filter: blur(10rpx);
		        backdrop-filter: blur(10rpx);
}
.form-item.data-v-4438b7d4 {
		display: flex;
		align-items: center;
		padding: 40rpx;
		border-bottom: 1rpx solid rgba(0,0,0,0.05);
}
.form-item.data-v-4438b7d4:last-child {
		border-bottom: none;
}
.form-label.data-v-4438b7d4 {
		width: 160rpx;
		font-size: 30rpx;
		color: #333;
		font-weight: 500;
}
.form-input.data-v-4438b7d4 {
		flex: 1;
		font-size: 30rpx;
		color: #333;
		text-align: right;
		background: transparent;
		border: none;
}
.form-input.data-v-4438b7d4::-webkit-input-placeholder {
		color: #ccc;
}
.form-input.data-v-4438b7d4::placeholder {
		color: #ccc;
}
	
	/* 性别选择器 */
.gender-selector.data-v-4438b7d4 {
		flex: 1;
		display: flex;
		justify-content: flex-end;
		gap: 16rpx;
}
.gender-option.data-v-4438b7d4 {
		padding: 16rpx 28rpx;
		border-radius: 40rpx;
		border: 2rpx solid rgba(255,107,157,0.3);
		transition: all 0.3s ease;
}
.gender-option.active.data-v-4438b7d4 {
		background: #ff6b9d;
		border-color: #ff6b9d;
}
.gender-text.data-v-4438b7d4 {
		font-size: 26rpx;
		color: #666;
}
.gender-option.active .gender-text.data-v-4438b7d4 {
		color: #fff;
}
	
	/* 选择器 */
.picker-wrapper.data-v-4438b7d4 {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: flex-end;
}
.picker-text.data-v-4438b7d4 {
		font-size: 30rpx;
		color: #333;
		margin-right: 12rpx;
}
.picker-arrow.data-v-4438b7d4 {
		font-size: 24rpx;
		color: #ccc;
}
	
	/* 个性签名 */
.signature-wrapper.data-v-4438b7d4 {
		background: rgba(255,255,255,0.95);
		border-radius: 24rpx;
		padding: 40rpx;
		box-shadow: 0 8rpx 24rpx rgba(255,154,158,0.2);
		-webkit-backdrop-filter: blur(10rpx);
		        backdrop-filter: blur(10rpx);
}
.signature-textarea.data-v-4438b7d4 {
		width: 100%;
		min-height: 120rpx;
		font-size: 30rpx;
		color: #333;
		line-height: 1.6;
		background: rgba(255,107,157,0.05);
		border-radius: 16rpx;
		padding: 24rpx;
		border: 1rpx solid rgba(255,107,157,0.1);
		margin-bottom: 16rpx;
}
.signature-textarea.data-v-4438b7d4::-webkit-input-placeholder {
		color: #ccc;
}
.signature-textarea.data-v-4438b7d4::placeholder {
		color: #ccc;
}
.char-counter.data-v-4438b7d4 {
		text-align: right;
		font-size: 24rpx;
		color: #999;
		display: block;
}
	
	/* 保存按钮 */
.save-section.data-v-4438b7d4 {
		margin-top: 60rpx;
		text-align: center;
}
.save-btn.data-v-4438b7d4 {
		width: 100%;
		background: linear-gradient(135deg, #ff6b9d, #c44569);
		color: #fff;
		border: none;
		border-radius: 50rpx;
		padding: 36rpx;
		font-size: 32rpx;
		font-weight: 600;
		box-shadow: 0 8rpx 24rpx rgba(196,69,105,0.3);
		transition: all 0.3s ease;
}
.save-btn.data-v-4438b7d4:active {
		transform: translateY(2rpx);
		box-shadow: 0 6rpx 20rpx rgba(196,69,105,0.4);
}
