"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      coupleStatus: "已连接",
      loveStartDate: "2023-08-04",
      currentTheme: "浪漫粉色",
      fontSize: "标准",
      cacheSize: "12.5MB",
      appVersion: "1.0.0",
      privacySettings: {
        dynamicVisible: true,
        locationShare: false,
        onlineStatus: true
      },
      notificationSettings: {
        message: true,
        anniversary: true,
        dailyQuote: true,
        system: false
      }
    };
  },
  methods: {
    // 个人设置
    editProfile() {
      if (common_vendor.index.getStorageSync("userInfo")) {
        common_vendor.index.navigateTo({
          url: "/pages/profile/edit-profile"
        });
      } else {
        common_vendor.index.navigateTo({
          url: "/pages/index/index"
        });
      }
    },
    accountSecurity() {
      common_vendor.index.showToast({ title: "账号与安全功能待开发", icon: "none" });
    },
    // 情侣关系设置
    coupleConnection() {
      common_vendor.index.showToast({ title: "情侣连接功能待开发", icon: "none" });
    },
    setLoveDate() {
      common_vendor.index.showToast({ title: "设置恋爱日期功能待开发", icon: "none" });
    },
    setCoupleNickname() {
      common_vendor.index.showToast({ title: "设置情侣昵称功能待开发", icon: "none" });
    },
    // 隐私设置开关
    toggleDynamicVisible(e) {
      this.privacySettings.dynamicVisible = e.detail.value;
      common_vendor.index.showToast({
        title: e.detail.value ? "已开启动态可见" : "已关闭动态可见",
        icon: "none"
      });
    },
    toggleLocationShare(e) {
      this.privacySettings.locationShare = e.detail.value;
      common_vendor.index.showToast({
        title: e.detail.value ? "已开启位置共享" : "已关闭位置共享",
        icon: "none"
      });
    },
    toggleOnlineStatus(e) {
      this.privacySettings.onlineStatus = e.detail.value;
      common_vendor.index.showToast({
        title: e.detail.value ? "已显示在线状态" : "已隐藏在线状态",
        icon: "none"
      });
    },
    // 通知设置开关
    toggleMessageNotification(e) {
      this.notificationSettings.message = e.detail.value;
      common_vendor.index.showToast({
        title: e.detail.value ? "已开启消息通知" : "已关闭消息通知",
        icon: "none"
      });
    },
    toggleAnniversaryNotification(e) {
      this.notificationSettings.anniversary = e.detail.value;
      common_vendor.index.showToast({
        title: e.detail.value ? "已开启纪念日提醒" : "已关闭纪念日提醒",
        icon: "none"
      });
    },
    toggleDailyQuoteNotification(e) {
      this.notificationSettings.dailyQuote = e.detail.value;
      common_vendor.index.showToast({
        title: e.detail.value ? "已开启每日情话" : "已关闭每日情话",
        icon: "none"
      });
    },
    toggleSystemNotification(e) {
      this.notificationSettings.system = e.detail.value;
      common_vendor.index.showToast({
        title: e.detail.value ? "已开启系统通知" : "已关闭系统通知",
        icon: "none"
      });
    },
    // 应用设置
    setTheme() {
      common_vendor.index.showActionSheet({
        itemList: ["浪漫粉色", "清新蓝色", "温暖橙色", "优雅紫色"],
        success: (res) => {
          const themes = ["浪漫粉色", "清新蓝色", "温暖橙色", "优雅紫色"];
          this.currentTheme = themes[res.tapIndex];
          common_vendor.index.showToast({ title: `已切换到${this.currentTheme}主题`, icon: "none" });
        }
      });
    },
    setFontSize() {
      common_vendor.index.showActionSheet({
        itemList: ["小", "标准", "大", "超大"],
        success: (res) => {
          const sizes = ["小", "标准", "大", "超大"];
          this.fontSize = sizes[res.tapIndex];
          common_vendor.index.showToast({ title: `字体大小已设置为${this.fontSize}`, icon: "none" });
        }
      });
    },
    clearCache() {
      common_vendor.index.showModal({
        title: "清除缓存",
        content: "确定要清除所有缓存数据吗？",
        success: (res) => {
          if (res.confirm) {
            this.cacheSize = "0MB";
            common_vendor.index.showToast({ title: "缓存清除成功", icon: "success" });
          }
        }
      });
    },
    // 关于与帮助
    checkVersion() {
      common_vendor.index.showToast({ title: "已是最新版本", icon: "success" });
    },
    userAgreement() {
      common_vendor.index.showToast({ title: "用户协议功能待开发", icon: "none" });
    },
    privacyPolicy() {
      common_vendor.index.showToast({ title: "隐私政策功能待开发", icon: "none" });
    },
    feedback() {
      common_vendor.index.showToast({ title: "意见反馈功能待开发", icon: "none" });
    },
    contactService() {
      common_vendor.index.showToast({ title: "联系客服功能待开发", icon: "none" });
    },
    // 退出登录
    logout() {
      common_vendor.index.showModal({
        title: "确认退出",
        content: "确定要退出登录吗？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({ title: "已退出登录", icon: "success" });
            setTimeout(() => {
              common_vendor.index.switchTab({ url: "/pages/index/index" });
            }, 1500);
          }
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.editProfile && $options.editProfile(...args)),
    b: common_vendor.o((...args) => $options.accountSecurity && $options.accountSecurity(...args)),
    c: common_vendor.t($data.coupleStatus),
    d: common_vendor.o((...args) => $options.coupleConnection && $options.coupleConnection(...args)),
    e: common_vendor.t($data.loveStartDate),
    f: common_vendor.o((...args) => $options.setLoveDate && $options.setLoveDate(...args)),
    g: common_vendor.o((...args) => $options.setCoupleNickname && $options.setCoupleNickname(...args)),
    h: $data.privacySettings.dynamicVisible,
    i: common_vendor.o((...args) => $options.toggleDynamicVisible && $options.toggleDynamicVisible(...args)),
    j: $data.privacySettings.locationShare,
    k: common_vendor.o((...args) => $options.toggleLocationShare && $options.toggleLocationShare(...args)),
    l: $data.privacySettings.onlineStatus,
    m: common_vendor.o((...args) => $options.toggleOnlineStatus && $options.toggleOnlineStatus(...args)),
    n: $data.notificationSettings.message,
    o: common_vendor.o((...args) => $options.toggleMessageNotification && $options.toggleMessageNotification(...args)),
    p: $data.notificationSettings.anniversary,
    q: common_vendor.o((...args) => $options.toggleAnniversaryNotification && $options.toggleAnniversaryNotification(...args)),
    r: $data.notificationSettings.dailyQuote,
    s: common_vendor.o((...args) => $options.toggleDailyQuoteNotification && $options.toggleDailyQuoteNotification(...args)),
    t: $data.notificationSettings.system,
    v: common_vendor.o((...args) => $options.toggleSystemNotification && $options.toggleSystemNotification(...args)),
    w: common_vendor.t($data.currentTheme),
    x: common_vendor.o((...args) => $options.setTheme && $options.setTheme(...args)),
    y: common_vendor.t($data.fontSize),
    z: common_vendor.o((...args) => $options.setFontSize && $options.setFontSize(...args)),
    A: common_vendor.t($data.cacheSize),
    B: common_vendor.o((...args) => $options.clearCache && $options.clearCache(...args)),
    C: common_vendor.t($data.appVersion),
    D: common_vendor.o((...args) => $options.checkVersion && $options.checkVersion(...args)),
    E: common_vendor.o((...args) => $options.userAgreement && $options.userAgreement(...args)),
    F: common_vendor.o((...args) => $options.privacyPolicy && $options.privacyPolicy(...args)),
    G: common_vendor.o((...args) => $options.feedback && $options.feedback(...args)),
    H: common_vendor.o((...args) => $options.contactService && $options.contactService(...args)),
    I: common_vendor.o((...args) => $options.logout && $options.logout(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-018cdf56"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/setting/setting.js.map
