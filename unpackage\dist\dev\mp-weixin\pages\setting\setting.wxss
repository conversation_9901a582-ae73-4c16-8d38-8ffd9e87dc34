
.setting-container.data-v-018cdf56 {
		min-height: 100vh;
		background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
		padding: 40rpx 30rpx;
}
	
	/* 页面标题 */
.page-header.data-v-018cdf56 {
		text-align: center;
		margin-bottom: 40rpx;
}
.page-title.data-v-018cdf56 {
		font-size: 40rpx;
		font-weight: 700;
		color: #fff;
		text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);
}
	
	/* 账户信息卡片 */
.account-card.data-v-018cdf56 {
		background: rgba(255,255,255,0.95);
		border-radius: 24rpx;
		padding: 40rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 8rpx 24rpx rgba(255,154,158,0.2);
		-webkit-backdrop-filter: blur(10rpx);
		        backdrop-filter: blur(10rpx);
}
.account-info.data-v-018cdf56 {
		display: flex;
		align-items: center;
}
.account-avatar.data-v-018cdf56 {
		width: 100rpx;
		height: 100rpx;
		border-radius: 50rpx;
		border: 3rpx solid #ff6b9d;
		margin-right: 30rpx;
}
.account-details.data-v-018cdf56 {
		flex: 1;
}
.account-name.data-v-018cdf56 {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		display: block;
		margin-bottom: 8rpx;
}
.account-phone.data-v-018cdf56 {
		font-size: 26rpx;
		color: #999;
}
.vip-badge.data-v-018cdf56 {
		background: linear-gradient(135deg, #ffd700, #ffb347);
		color: #fff;
		font-size: 20rpx;
		font-weight: 600;
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		box-shadow: 0 4rpx 12rpx rgba(255,215,0,0.3);
}
	
	/* 设置分组 */
.setting-group.data-v-018cdf56 {
		margin-bottom: 30rpx;
}
.group-title.data-v-018cdf56 {
		font-size: 28rpx;
		font-weight: 600;
		color: rgba(255,255,255,0.9);
		margin-bottom: 20rpx;
		margin-left: 20rpx;
		display: block;
}
.setting-list.data-v-018cdf56 {
		background: rgba(255,255,255,0.95);
		border-radius: 24rpx;
		overflow: hidden;
		box-shadow: 0 8rpx 24rpx rgba(255,154,158,0.2);
		-webkit-backdrop-filter: blur(10rpx);
		        backdrop-filter: blur(10rpx);
}
.setting-item.data-v-018cdf56 {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 40rpx;
		border-bottom: 1rpx solid rgba(0,0,0,0.05);
		transition: background-color 0.3s ease;
}
.setting-item.data-v-018cdf56:last-child {
		border-bottom: none;
}
.setting-item.data-v-018cdf56:active {
		background-color: rgba(255,107,157,0.05);
}
.item-left.data-v-018cdf56 {
		display: flex;
		align-items: center;
		flex: 1;
}
.item-icon.data-v-018cdf56 {
		font-size: 32rpx;
		margin-right: 24rpx;
		width: 40rpx;
		text-align: center;
}
.item-text.data-v-018cdf56 {
		font-size: 30rpx;
		color: #333;
		font-weight: 500;
}
.item-right.data-v-018cdf56 {
		display: flex;
		align-items: center;
}
.item-value.data-v-018cdf56 {
		font-size: 26rpx;
		color: #999;
		margin-right: 16rpx;
}
.item-arrow.data-v-018cdf56 {
		font-size: 24rpx;
		color: #ccc;
}
.setting-switch.data-v-018cdf56 {
		transform: scale(0.8);
}
	
	/* 退出登录 */
.logout-section.data-v-018cdf56 {
		margin-top: 60rpx;
		text-align: center;
}
.logout-btn.data-v-018cdf56 {
		width: 100%;
		background: rgba(255,255,255,0.95);
		color: #ff6b9d;
		border: 2rpx solid #ff6b9d;
		border-radius: 50rpx;
		padding: 32rpx;
		font-size: 30rpx;
		font-weight: 600;
		box-shadow: 0 6rpx 20rpx rgba(255,154,158,0.15);
		transition: all 0.3s ease;
}
.logout-btn.data-v-018cdf56:active {
		background: rgba(255,107,157,0.1);
		transform: translateY(2rpx);
}
