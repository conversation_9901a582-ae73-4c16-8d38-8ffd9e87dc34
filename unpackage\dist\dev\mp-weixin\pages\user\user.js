"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      userInfo: null,
      isLogin: false
    };
  },
  methods: {
    //退出登录
    handleLogout() {
      common_vendor.index.showModal({
        title: "退出登录",
        content: "请确认是否要退出?",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.removeStorageSync("userInfo");
            common_vendor.index.showToast({
              title: "已退出登录",
              icon: "success"
            });
            common_vendor.index.navigateTo({
              url: "/pages/login/login"
            });
          }
        },
        fail: (err) => {
          common_vendor.index.showToast({
            title: "退出登录失败",
            icon: "none"
          });
        }
      });
    },
    //检查登录状态
    checkLoginStatus() {
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      if (userInfo) {
        this.userInfo = userInfo;
        this.isLogin = true;
      } else {
        this.userInfo = null;
        this.isLogin = false;
      }
    }
  },
  onShow() {
    this.checkLoginStatus();
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.t($data.userInfo.nickName),
    b: common_vendor.o(($event) => _ctx.navigateTo("/pages/profile/edit-profile")),
    c: common_assets._imports_0,
    d: common_assets._imports_0,
    e: common_vendor.o((...args) => $options.handleLogout && $options.handleLogout(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-0f7520f0"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/user/user.js.map
