
.user-container.data-v-0f7520f0 {
		min-height: 100vh;
		background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
		padding: 40rpx 30rpx;
}
	
	/* 个人信息卡片 */
.profile-card.data-v-0f7520f0 {
		background: rgba(255,255,255,0.95);
		border-radius: 24rpx;
		padding: 40rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 8rpx 24rpx rgba(255,154,158,0.2);
		-webkit-backdrop-filter: blur(10rpx);
		        backdrop-filter: blur(10rpx);
}
.profile-header.data-v-0f7520f0 {
		display: flex;
		align-items: center;
}
.avatar-section.data-v-0f7520f0 {
		position: relative;
		margin-right: 30rpx;
}
.user-avatar.data-v-0f7520f0 {
		width: 120rpx;
		height: 120rpx;
		border-radius: 60rpx;
		border: 4rpx solid #ff6b9d;
}
.online-dot.data-v-0f7520f0 {
		position: absolute;
		bottom: 5rpx;
		right: 5rpx;
		width: 24rpx;
		height: 24rpx;
		background: #4caf50;
		border: 3rpx solid #fff;
		border-radius: 50%;
}
.user-info.data-v-0f7520f0 {
		flex: 1;
}
.username.data-v-0f7520f0 {
		font-size: 36rpx;
		font-weight: 600;
		color: #333;
		display: block;
		margin-bottom: 8rpx;
}
.user-status.data-v-0f7520f0 {
		font-size: 26rpx;
		color: #4caf50;
}
.edit-btn.data-v-0f7520f0 {
		background: linear-gradient(135deg, #ff6b9d, #c44569);
		color: #fff;
		border: none;
		border-radius: 40rpx;
		padding: 16rpx 32rpx;
		font-size: 26rpx;
		font-weight: 500;
}
	
	/* 情侣关系卡片 */
.relationship-card.data-v-0f7520f0 {
		background: rgba(255,255,255,0.95);
		border-radius: 24rpx;
		padding: 40rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 8rpx 24rpx rgba(255,154,158,0.2);
		-webkit-backdrop-filter: blur(10rpx);
		        backdrop-filter: blur(10rpx);
}
.relationship-header.data-v-0f7520f0 {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;
}
.card-title.data-v-0f7520f0 {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
}
.love-status.data-v-0f7520f0 {
		font-size: 24rpx;
		color: #ff6b9d;
		background: rgba(255,107,157,0.1);
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
}
.couple-info.data-v-0f7520f0 {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 40rpx;
}
.couple-avatar-section.data-v-0f7520f0 {
		text-align: center;
		flex: 1;
}
.couple-avatar.data-v-0f7520f0 {
		width: 80rpx;
		height: 80rpx;
		border-radius: 40rpx;
		border: 3rpx solid #ff6b9d;
		margin-bottom: 10rpx;
}
.couple-name.data-v-0f7520f0 {
		font-size: 24rpx;
		color: #666;
		display: block;
}
.love-connection.data-v-0f7520f0 {
		flex: 1;
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
}
.love-line.data-v-0f7520f0 {
		width: 80rpx;
		height: 2rpx;
		background: #ff6b9d;
}
.love-heart.data-v-0f7520f0 {
		position: absolute;
		font-size: 24rpx;
		animation: pulse-0f7520f0 2s infinite;
}
.love-stats.data-v-0f7520f0 {
		display: flex;
		justify-content: space-around;
		background: rgba(255,107,157,0.05);
		border-radius: 16rpx;
		padding: 30rpx;
}
.stat-item.data-v-0f7520f0 {
		text-align: center;
}
.stat-number.data-v-0f7520f0 {
		font-size: 32rpx;
		font-weight: 700;
		color: #ff6b9d;
		display: block;
		margin-bottom: 8rpx;
}
.stat-label.data-v-0f7520f0 {
		font-size: 24rpx;
		color: #999;
}
	
	/* 功能菜单 */
.menu-grid.data-v-0f7520f0 {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 20rpx;
		margin-bottom: 40rpx;
}
.menu-item.data-v-0f7520f0 {
		background: rgba(255,255,255,0.95);
		border-radius: 20rpx;
		padding: 40rpx 20rpx;
		text-align: center;
		box-shadow: 0 6rpx 20rpx rgba(255,154,158,0.15);
		transition: all 0.3s ease;
}
.menu-item.data-v-0f7520f0:active {
		transform: scale(0.95);
		box-shadow: 0 4rpx 16rpx rgba(255,154,158,0.25);
}
.menu-icon.data-v-0f7520f0 {
		font-size: 40rpx;
		margin-bottom: 16rpx;
		display: block;
}
.menu-text.data-v-0f7520f0 {
		font-size: 26rpx;
		color: #333;
		font-weight: 500;
}
	
	/* 退出登录 */
.logout-section.data-v-0f7520f0 {
		text-align: center;
}
.logout-btn.data-v-0f7520f0 {
		width: 100%;
		background: rgba(255,255,255,0.95);
		color: #ff6b9d;
		border: 2rpx solid #ff6b9d;
		border-radius: 50rpx;
		padding: 32rpx;
		font-size: 30rpx;
		font-weight: 600;
		box-shadow: 0 6rpx 20rpx rgba(255,154,158,0.15);
		transition: all 0.3s ease;
}
.logout-btn.data-v-0f7520f0:active {
		background: rgba(255,107,157,0.1);
		transform: translateY(2rpx);
}
	
	/* 动画 */
@keyframes pulse-0f7520f0 {
0%, 100% {
			transform: scale(1);
}
50% {
			transform: scale(1.1);
}
}
